#ifndef __HAL_GPIO_H__
#define __HAL_GPIO_H__

#include "stdint.h"

// clang-format off
enum gpio_pin {
    PA0, PA1, PA2, PA3, PA4, PA5, PA6, PA7, PA8, PA9, PA10, PA11, PA12, PA13, PA14, PA15,
    PB0, PB1, PB2, PB3, PB4, PB5, PB6, PB7, PB8, PB9, PB10, PB11, PB12, PB13, PB14, PB15,
    PC0, PC1, PC2, PC3, PC4, PC5, PC6, PC7, PC8, PC9, PC10, PC11, PC12, PC13, PC14, PC15,
    PD0, PD1, PD2, PD3, PD4, PD5, PD6, PD7, PD8, PD9, PD10, PD11, PD12, PD13, PD14, PD15,
    PE0, PE1, PE2, PE3, PE4, PE5, PE6, PE7, PE8, PE9, PE10, PE11, PE12, PE13, PE14, PE15,
    PF0, PF1, PF2, PF3, PF4, PF5, PF6, PF7, PF8, PF9, PF10, PF11, PF12, PF13, PF14, PF15,
    PG0, PG1, PG2, PG3, PG4, PG5, PG6, PG7, PG8, PG9, PG10, PG11, PG12, PG13, PG14, PG15,
    TEMPSENSOR, VBAT, VSYSC, VDDA
};
// clang-format on

enum gpio_status
{
    GPIO_LOW,
    GPIO_HIGH,
};

enum gpio_mode
{
    INPUT,
    OUT_PP,
    OUT_OD,
    AF_PP,
    AF_OD,
    TI_RISING,
    TI_FALLING,
    TI_RISING_FALLING,
    ANALOG
};

enum gpio_pull
{
    NOPULL,
    PULLUP,
    PULLDOWN
};

enum gpio_speed
{
    LOW,
    MEDIUM,
    HIGH,
    VERY_HIGH
};

enum gpio_alternate
{
    AF_UART1 = 1,
    AF_UART2,
    AF_UART3,

    AF_TIM1 = 32,
    AF_TIM2,
    AF_TIM3,
    AF_TIM4,
    AF_TIM5,
    AF_TIM6,
    AF_TIM7,
    AF_TIM8,
    AF_TIM9,
    AF_TIM10,
    AF_TIM11,
    AF_TIM12,
    AF_TIM13,
    AF_TIM14

};

/**
 * @brief       初始化GPIO引脚
 * @param       pin       引脚号
 * @param       mode       引脚模式: 0-输入 1-输出 2-开漏输出 3-复用推挽输出 4-复用开漏输出
 * @param       mode       引脚模式: 5-上拉输入中断 6-下拉输入中断 7-上下拉输入中断 8-模拟模式
 * @param       pull       引脚上下拉: 0-无上下拉 1-上拉 2-下拉
 * @param       speed      引脚速度: 0-低速 1-中速 2-高速 3-超高速
 * @note        无
 * @retval      无
 */
void gpio_pin_init(uint8_t pin, uint8_t mode, uint8_t pull, uint8_t speed);

/**
 * @brief       初始化GPIO引脚
 * @param       pin       引脚号
 * @param       mode       引脚模式: 0-输入 1-输出 2-开漏输出 3-复用推挽输出 4-复用开漏输出 5-上拉输入中断 6-下拉输入中断 7-上下拉输入中断 8-模拟模式
 * @param       pull       引脚上下拉: 0-无上下拉 1-上拉 2-下拉
 * @param       speed      引脚速度: 0-低速 1-中速 2-高速 3-超高速
 * @param       alternate  引脚复用：参考枚举值gpio_alternate
 * @note        无
 * @retval      无
 */
void gpio_pin_init_alternate(uint8_t pin, uint8_t mode, uint8_t pull, uint8_t speed, uint8_t alternate);

/**
 * @brief       设置GPIO引脚值
 * @param       pin       引脚号
 * @param       value     引脚值
 * @note        无
 * @retval      无
 */
void gpio_write_pin(uint8_t pin, uint8_t value);

/**
 * @brief       读取GPIO引脚值
 * @param       pin       引脚号
 * @note        无
 * @retval      引脚值
 */
uint8_t gpio_read_pin(uint8_t pin);

/**
 * @brief       切换GPIO引脚值
 * @param       pin       引脚号
 * @note        无
 * @retval      无
 */
void gpio_toggle_pin(uint8_t pin);

#endif
