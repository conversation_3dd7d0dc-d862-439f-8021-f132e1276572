#include "./sdk_main.h"
#include "./middleware/rtos/os_abstract.h"
#include "./system/sys/sys.h"
#include "./drivers/devices/input/key/key.h"
#include "./system/event/app_event.h"
#include "./system/event/system_event.h"
#include "./system/debug/debug.h"
#include "./drivers/core/driver_core.h"
#include "stdio.h"
#include "./drivers/devices/display/oled/driver_oled.h"

#include "device.h"

void display_show_init(void)
{
#if TCFG_DISPLAY_EN == TCFG_ENABLE
    OLED_Init();
#endif
}

void board_init(void)
{
    gpio_pin_init(PB2, OUT_PP, NOPULL, LOW);
    key_init();
    display_show_init();
}

void system_init(void)
{
    debug_init();
    sys_check_rst();
    // iwdg_init(IWDG_PRESCALER_64, 500);
    sys_event_init();
    adc_pin_init(1, PA0, 0, 0);
    adc_pin_init(1, TEMPSENSOR, 0, 0);
}

void app_init(void)
{
    app_event_init();
}

float get_temperature_mcu(void)
{
    uint16_t adcx;
    float Temperature;
    adcx = adc_get_value(TEMPSENSOR);
    Temperature = (float)adcx * (3.3 / 4095);           // 计算传感器的电压值给到温度
    Temperature = (Temperature - 0.76f) / 0.0025f + 25; // 通过公式计算出来的温度值
    return Temperature;
}

void sdk_main(void *arg)
{
    system_init();
    board_init();
    app_init();
    _sys_device_do_config_level(_INIT_LEVEL_DRIVER);
    tim_pwm_pin_init(4, 4, PB9, 100 - 1, 840 - 1); // 84M/84=1M,1M/500=2KHz
    tim_pwm_set_ccr(3, 2, 50);
    DEBUG_INFO("system init ok!\r\n");
    static os_tick tick = 0;
    tick = os_task_get_tick();
    while (1)
    {
        debug_printf("sdk_main\r\n");
        debug_printf("adc_value PA0: %d\r\n", adc_get_value(PA0));
        debug_printf("mcu temperature: %.2f℃ \r\n", get_temperature_mcu());
        gpio_write_pin(PB2, 0);
        os_task_delay_until(&tick, 500);
        gpio_write_pin(PB2, 1);
        os_task_delay_until(&tick, 500);

        static uint16_t aaa = 0;
        /*在(96, 18)位置打印格式化字符串，字体大小为6*8点阵，格式化字符串为"[%02d]"*/
        OLED_Printf(50, 18, OLED_8X16, "[%d]", aaa++);

        /*调用OLED_Update函数，将OLED显存数组的内容更新到OLED硬件进行显示*/
        OLED_Update();
    }
}

void iwdg_feed_task(void *arg)
{
    static os_tick tick = 0;
    tick = os_task_get_tick();
    while (1)
    {
        os_task_delay_until(&tick, 100);
        iwdg_feed();
        // debug_printf("iwdg_feed\r\n");
    }
}

void sdk_init(void)
{
    os_task_create(sdk_main, "sdk_main", 128, NULL, 3, NULL);
    os_task_create(iwdg_feed_task, "iwdg_feed_task", 32, NULL, 0, NULL);
    os_start();
}
