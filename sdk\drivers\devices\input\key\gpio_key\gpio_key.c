#include "./system/sys/sys.h"
#include "./middleware/rtos/os_abstract.h"
#include "./drivers/devices/input/key/key.h"
#include "./drivers/devices/input/key/gpio_key/gpio_key.h"
#include "./system/event/app_event.h"
#include "./system/event/app_msg.h"
#include "./config/board_config.h"
#include "./system/debug/debug.h"
#include "stdint.h"
#include "./drivers/core/driver_core.h"

#if TCFG_IO_KEY_EN == TCFG_ENABLE

static uint8_t gpio_key_get_value(void);

extern const gpio_key_platform_data_t gpio_key_data;

key_driver_para_t gpio_key_scan_para = {
    .scan_time = 10,
    .last_key = 0,
    .filter_time = 2,
    .long_time = 75,
    .hold_time = 75 + 25,
    .click_delay_time = 20,
    .get_value = gpio_key_get_value,
    .key_type = KEY_DRIVER_TYPE_IO,
};

static void gpio_key_scan(os_timer_handle_t timer)
{
    static uint8_t key_event = 0;
    static uint8_t key_value = 0;
    struct sys_event e;
    const uint8_t NO_KEY = 0xFF;
    uint8_t cur_key_value = NO_KEY;

    cur_key_value = gpio_key_scan_para.get_value();
    //===== 按键消抖处理
    /*
        if (cur_key_value != gpiokey_scan_para.filter_value && gpiokey_scan_para.filter_time)
        {
            gpiokey_scan_para.filter_cnt = 0;
            gpiokey_scan_para.filter_value = cur_key_value;
            return;
        }
        if (gpiokey_scan_para.filter_cnt < gpiokey_scan_para.filter_time)
        {
            gpiokey_scan_para.filter_cnt++;
            return;
        }
    */
    //===== 按键消抖结束, 开始判断按键类型(单击, 双击, 长按, 多击, HOLD, (长按/HOLD)抬起)
    if (cur_key_value != gpio_key_scan_para.last_key)
    {
        if (cur_key_value == NO_KEY)
        { // 按键被抬起
            if (gpio_key_scan_para.press_cnt >= gpio_key_scan_para.long_time)
            {
                key_event = KEY_EVENT_UP;
                key_value = gpio_key_scan_para.last_key;
                // debug_message("ADKEY", "KEY_EVENT_UP: %d\n", key_value);
                goto _notify;
            }
            gpio_key_scan_para.click_delay_cnt = 1;
        }
        else
        { // 按键被按下
            gpio_key_scan_para.press_cnt = 1;
            if (cur_key_value != gpio_key_scan_para.notify_value)
            {
                gpio_key_scan_para.click_cnt = 1;
                gpio_key_scan_para.notify_value = cur_key_value;
                // debug_message("ADKEY", "KEY_EVENT_DOWN: %d\n", cur_key_value);
            }
            else
            {
                gpio_key_scan_para.click_cnt++;
                // debug_message("ADKEY", "KEY_EVENT_REPEAT: %d\n", cur_key_value);
            }
        }
        goto _scan_end;
    }
    else
    {
        if (cur_key_value == NO_KEY)
        {
            if (gpio_key_scan_para.click_cnt == 1)
            {
                key_event = KEY_EVENT_CLICK;
                // debug_message("ADKEY", "KEY_EVENT_CLICK: %d\n", gpio_key_scan_para.notify_value);
                key_value = gpio_key_scan_para.notify_value;
                goto _notify;
            }
        }
        else
        {
            gpio_key_scan_para.press_cnt++;
            if (gpio_key_scan_para.press_cnt == gpio_key_scan_para.long_time)
            {
                key_event = KEY_EVENT_LONG;
                // debug_message("ADKEY", "KEY_EVENT_LONG: %d\n", cur_key_value);
            }
            else if (gpio_key_scan_para.press_cnt == gpio_key_scan_para.hold_time)
            {
                key_event = KEY_EVENT_HOLD;
                gpio_key_scan_para.press_cnt = gpio_key_scan_para.long_time;
                // debug_message("ADKEY", "KEY_EVENT_HOLD: %d\n", cur_key_value);
            }
            else
            {
                goto _scan_end;
            }
            key_value = cur_key_value;
            goto _notify;
        }
    }
    return;
_notify:
    e.type = SYS_KEY_EVENT;
    e.u.key.init = 1;
    e.u.key.type = gpio_key_scan_para.key_type;
    e.u.key.event = key_event;
    e.u.key.value = key_value;

    gpio_key_scan_para.click_cnt = 0;
    gpio_key_scan_para.notify_value = NO_KEY;

    e.arg = (void *)DEVICE_EVENT_FROM_KEY;

    sys_event_send_notify(&e);

_scan_end:
    gpio_key_scan_para.last_key = cur_key_value;
}

static uint8_t gpio_key_get_value(void)
{
    for (uint8_t i = 0; i < TCFG_IO_KEY_USER_MAX; i++)
    {
        if (gpio_read_pin(gpio_key_data.pin[i]) == gpio_key_data.click_status[i])
            return gpio_key_data.key_value[i];
    }
    return 0xFF;
}

static os_timer_handle_t gpio_key_scan_handler;
static void gpio_key_scan(os_timer_handle_t timer);

void gpio_key_init(void)
{

    for (uint8_t i = 0; i < TCFG_IO_KEY_USER_MAX; i++)
    {
        if (gpio_key_data.click_status[i] == 0) // 按下值为0时,配置上拉
            gpio_pin_init(gpio_key_data.pin[i], INPUT, PULLUP, GPIO_SPEED_FREQ_LOW);
        else if (gpio_key_data.click_status[i] == 1) // 按下值为1时,配置下拉
            gpio_pin_init(gpio_key_data.pin[i], INPUT, PULLDOWN, GPIO_SPEED_FREQ_LOW);
    }

    gpio_key_scan_handler = os_timer_create("gpio_key_scan", gpio_key_scan, NULL, gpio_key_scan_para.scan_time);
    os_timer_start(gpio_key_scan_handler, os_wait_max);
}

// 应用

const uint16_t gpio_key_table[10][4] =
    {
        /* 单击     长按        按住        抬起     */
        {MSG_NULL, MSG_NULL, MSG_NULL, MSG_NULL}, // 补充0行,对应按键1开始
        {MSG_POWER_ON, MSG_NULL, MSG_POWER_OFF, MSG_NULL},
        {MSG_NULL, MSG_POWER_OFF, MSG_POWER_OFF, MSG_NULL},
        {MSG_LCD_SHOW_ON, MSG_NULL, MSG_NULL, MSG_NULL},
        {MSG_LCD_SHOW_OFF, MSG_NULL, MSG_NULL, MSG_NULL},
        {MSG_NULL, MSG_NULL, MSG_NULL, MSG_NULL},
        {MSG_NULL, MSG_NULL, MSG_NULL, MSG_NULL},
        {MSG_NULL, MSG_NULL, MSG_NULL, MSG_NULL},
        {MSG_NULL, MSG_NULL, MSG_NULL, MSG_NULL},
        {MSG_NULL, MSG_NULL, MSG_NULL, MSG_NULL},
};

void process_gpio_key_event(struct key_event *e)
{
    DEBUG_INFO("key event: %d, value: %d\r\n", e->event, e->value);
    switch (e->event)
    {
    case KEY_EVENT_CLICK:
        app_msg_send_notify(gpio_key_table[e->value][e->event]);
        break;
    case KEY_EVENT_LONG:
        app_msg_send_notify(gpio_key_table[e->value][e->event]);
        break;
    case KEY_EVENT_HOLD:
        app_msg_send_notify(gpio_key_table[e->value][e->event]);
        break;
    case KEY_EVENT_UP:
        app_msg_send_notify(gpio_key_table[e->value][e->event]);
        break;
    default:
        DEBUG_WARN("unknown key event: %d, value: %d", e->event, e->value);
        break;
    }
}

#endif
