--cpu=Cortex-M4.fp.sp
"..\output\startup_stm32f407xx.o"
"..\output\stm32f4xx_hal.o"
"..\output\stm32f4xx_hal_dma_ex.o"
"..\output\stm32f4xx_hal_gpio.o"
"..\output\stm32f4xx_hal_pwr.o"
"..\output\stm32f4xx_hal_pwr_ex.o"
"..\output\stm32f4xx_hal_rcc.o"
"..\output\stm32f4xx_hal_rcc_ex.o"
"..\output\stm32f4xx_hal_uart.o"
"..\output\stm32f4xx_hal_cortex.o"
"..\output\stm32f4xx_hal_dma.o"
"..\output\stm32f4xx_hal_adc.o"
"..\output\stm32f4xx_hal_adc_ex.o"
"..\output\stm32f4xx_hal_iwdg.o"
"..\output\stm32f4xx_hal_spi.o"
"..\output\stm32f4xx_hal_tim.o"
"..\output\stm32f4xx_hal_tim_ex.o"
"..\output\main.o"
"..\output\stm32f4xx_it.o"
"..\output\system_stm32f4xx.o"
"..\output\sys.o"
"..\output\delay.o"
"..\output\debug.o"
"..\output\app_event.o"
"..\output\system_event.o"
"..\output\sdk_main.o"
"..\output\croutine.o"
"..\output\event_groups.o"
"..\output\list.o"
"..\output\queue.o"
"..\output\stream_buffer.o"
"..\output\tasks.o"
"..\output\timers.o"
"..\output\heap_4.o"
"..\output\port.o"
"..\output\key.o"
"..\output\gpio_key.o"
"..\output\adc_key.o"
"..\output\device.o"
"..\output\gpio_led.o"
"..\output\board_config.o"
"..\output\adc.o"
"..\output\gpio.o"
"..\output\i2c.o"
"..\output\spi.o"
"..\output\tim.o"
"..\output\uart.o"
"..\output\wdg.o"
"..\output\cjson.o"
"..\output\driver_oled.o"
"..\output\driver_oled_data.o"
--library_type=microlib --strict --scatter "..\Output\project.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "..\Output\project.map" -o ..\Output\project.axf