#include "./system/sys/sys.h"
#include "./drivers/core/driver_core.h"

typedef struct
{
    GPIO_TypeDef *port;
    uint16_t pin;
} gpio_config_t;

// clang-format off
/* 引脚映射表 */
const gpio_config_t gpio_map[] =
{
    {GPIOA, GPIO_PIN_0},{GPIOA, GPIO_PIN_1}, {GPIOA, GPIO_PIN_2},{GPIOA, GPIO_PIN_3}, {GPIOA, GPIO_PIN_4},{GPIOA, GPIO_PIN_5},{GPIOA, GPIO_PIN_6},{GPIOA, GPIO_PIN_7},
    {GPIOA, GPIO_PIN_8},{GPIOA, GPIO_PIN_9},{GPIOA, GPIO_PIN_10},{GPIOA, GPIO_PIN_11},{GPIOA, GPIO_PIN_12},{GPIOA, GPIO_PIN_13},{GPIOA, GPIO_PIN_14},{GPIOA, GPIO_PIN_15},
    {GPIOB, GP<PERSON>_PIN_0},{GPIOB, GPIO_PIN_1},{GPIOB, GPIO_PIN_2},{GPIOB, GPIO_PIN_3},{GPIOB, GPIO_PIN_4},{GPIOB, GPIO_PIN_5},{GPIOB, GPIO_PIN_6},{GPIOB, GPIO_PIN_7},
    {GPIOB, GPIO_PIN_8},{GPIOB, GPIO_PIN_9},{GPIOB, GPIO_PIN_10},{GPIOB, GPIO_PIN_11},{GPIOB, GPIO_PIN_12},{GPIOB, GPIO_PIN_13},{GPIOB, GPIO_PIN_14},{GPIOB, GPIO_PIN_15},
    {GPIOC, GPIO_PIN_0},{GPIOC, GPIO_PIN_1},{GPIOC, GPIO_PIN_2},{GPIOC, GPIO_PIN_3},{GPIOC, GPIO_PIN_4},{GPIOC, GPIO_PIN_5},{GPIOC, GPIO_PIN_6},{GPIOC, GPIO_PIN_7},
    {GPIOC, GPIO_PIN_8},{GPIOC, GPIO_PIN_9},{GPIOC, GPIO_PIN_10},{GPIOC, GPIO_PIN_11},{GPIOC, GPIO_PIN_12},{GPIOC, GPIO_PIN_13},{GPIOC, GPIO_PIN_14},{GPIOC, GPIO_PIN_15},{GPIOD, GPIO_PIN_0},{GPIOD, GPIO_PIN_1},{GPIOD, GPIO_PIN_2},{GPIOD, GPIO_PIN_3},{GPIOD, GPIO_PIN_4},{GPIOD, GPIO_PIN_5},{GPIOD, GPIO_PIN_6},{GPIOD, GPIO_PIN_7},
    {GPIOD, GPIO_PIN_8},{GPIOD, GPIO_PIN_9},{GPIOD, GPIO_PIN_10},{GPIOD, GPIO_PIN_11},{GPIOD, GPIO_PIN_12},{GPIOD, GPIO_PIN_13},{GPIOD, GPIO_PIN_14},{GPIOD, GPIO_PIN_15},
    {GPIOE, GPIO_PIN_0},{GPIOE, GPIO_PIN_1},{GPIOE, GPIO_PIN_2},{GPIOE, GPIO_PIN_3},{GPIOE, GPIO_PIN_4},{GPIOE, GPIO_PIN_5},{GPIOE, GPIO_PIN_6},{GPIOE, GPIO_PIN_7},
    {GPIOE, GPIO_PIN_8},{GPIOE, GPIO_PIN_9},{GPIOE, GPIO_PIN_10},{GPIOE, GPIO_PIN_11},{GPIOE, GPIO_PIN_12},{GPIOE, GPIO_PIN_13},{GPIOE, GPIO_PIN_14},{GPIOE, GPIO_PIN_15},
    {GPIOF, GPIO_PIN_0},{GPIOF, GPIO_PIN_1},{GPIOF, GPIO_PIN_2},{GPIOF, GPIO_PIN_3},{GPIOF, GPIO_PIN_4},{GPIOF, GPIO_PIN_5},{GPIOF, GPIO_PIN_6},{GPIOF, GPIO_PIN_7},
    {GPIOF, GPIO_PIN_8},{GPIOF, GPIO_PIN_9},{GPIOF, GPIO_PIN_10},{GPIOF, GPIO_PIN_11},{GPIOF, GPIO_PIN_12},{GPIOF, GPIO_PIN_13},{GPIOF, GPIO_PIN_14},{GPIOF, GPIO_PIN_15},
    {GPIOG, GPIO_PIN_0},{GPIOG, GPIO_PIN_1},{GPIOG, GPIO_PIN_2},{GPIOG, GPIO_PIN_3},{GPIOG, GPIO_PIN_4},{GPIOG, GPIO_PIN_5},{GPIOG, GPIO_PIN_6},{GPIOG, GPIO_PIN_7},
    {GPIOG, GPIO_PIN_8},{GPIOG, GPIO_PIN_9},{GPIOG, GPIO_PIN_10},{GPIOG, GPIO_PIN_11},{GPIOG, GPIO_PIN_12},{GPIOG, GPIO_PIN_13},{GPIOG, GPIO_PIN_14},{GPIOG, GPIO_PIN_15},
};
// clang-format on

/**
 * @brief       初始化GPIO引脚
 * @param       pin       引脚号
 * @param       mode       引脚模式: 0-输入 1-输出 2-开漏输出 3-复用推挽输出 4-复用开漏输出
 * @param       mode       引脚模式: 5-上拉输入中断 6-下拉输入中断 7-上下拉输入中断 8-模拟模式
 * @param       pull       引脚上下拉: 0-无上下拉 1-上拉 2-下拉
 * @param       speed      引脚速度: 0-低速 1-中速 2-高速 3-超高速
 * @note        无
 * @retval      无
 */
void gpio_pin_init(uint8_t pin, uint8_t mode, uint8_t pull, uint8_t speed)
{
    const gpio_config_t *config = &gpio_map[pin];
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    if (config->port == GPIOA)
        __HAL_RCC_GPIOA_CLK_ENABLE();
    else if (config->port == GPIOB)
        __HAL_RCC_GPIOB_CLK_ENABLE();
    else if (config->port == GPIOC)
        __HAL_RCC_GPIOC_CLK_ENABLE();
    else if (config->port == GPIOD)
        __HAL_RCC_GPIOD_CLK_ENABLE();
    else if (config->port == GPIOE)
        __HAL_RCC_GPIOE_CLK_ENABLE();
    else if (config->port == GPIOF)
        __HAL_RCC_GPIOF_CLK_ENABLE();
    else if (config->port == GPIOG)
        __HAL_RCC_GPIOG_CLK_ENABLE();

    GPIO_InitStruct.Pin = config->pin;

    if (mode == 0)
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    else if (mode == 1)
        GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    else if (mode == 2)
        GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    else if (mode == 3)
        GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    else if (mode == 4)
        GPIO_InitStruct.Mode = GPIO_MODE_AF_OD;
    else if (mode == 5)
        GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING;
    else if (mode == 6)
        GPIO_InitStruct.Mode = GPIO_MODE_IT_FALLING;
    else if (mode == 7)
        GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING_FALLING;
    else if (mode == 8)
        GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;

    if (pull == 0)
        GPIO_InitStruct.Pull = GPIO_NOPULL;
    else if (pull == 1)
        GPIO_InitStruct.Pull = GPIO_PULLUP;
    else if (pull == 2)
        GPIO_InitStruct.Pull = GPIO_PULLDOWN;

    if (speed == 0)
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    else if (speed == 1)
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_MEDIUM;
    else if (speed == 2)
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    else if (speed == 3)
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;

    HAL_GPIO_Init(config->port, &GPIO_InitStruct);
}

/**
 * @brief       复用模式初始化GPIO引脚
 * @param       pin       引脚号
 * @param       mode       引脚模式: 0-输入 1-输出 2-开漏输出 3-复用推挽输出 4-复用开漏输出 5-上拉输入中断 6-下拉输入中断 7-上下拉输入中断 8-模拟模式
 * @param       pull       引脚上下拉: 0-无上下拉 1-上拉 2-下拉
 * @param       speed      引脚速度: 0-低速 1-中速 2-高速 3-超高速
 * @param       alternate  引脚复用功能
 * @note        无
 * @retval      无
 */
void gpio_pin_init_alternate(uint8_t pin, uint8_t mode, uint8_t pull, uint8_t speed, uint8_t alternate)
{
    const gpio_config_t *config = &gpio_map[pin];
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    if (config->port == GPIOA)
        __HAL_RCC_GPIOA_CLK_ENABLE();
    else if (config->port == GPIOB)
        __HAL_RCC_GPIOB_CLK_ENABLE();
    else if (config->port == GPIOC)
        __HAL_RCC_GPIOC_CLK_ENABLE();
    else if (config->port == GPIOD)
        __HAL_RCC_GPIOD_CLK_ENABLE();
    else if (config->port == GPIOE)
        __HAL_RCC_GPIOE_CLK_ENABLE();
    else if (config->port == GPIOF)
        __HAL_RCC_GPIOF_CLK_ENABLE();
    else if (config->port == GPIOG)
        __HAL_RCC_GPIOG_CLK_ENABLE();

    GPIO_InitStruct.Pin = config->pin;

    if (mode == 0)
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    else if (mode == 1)
        GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    else if (mode == 2)
        GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    else if (mode == 3)
        GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    else if (mode == 4)
        GPIO_InitStruct.Mode = GPIO_MODE_AF_OD;
    else if (mode == 5)
        GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING;
    else if (mode == 6)
        GPIO_InitStruct.Mode = GPIO_MODE_IT_FALLING;
    else if (mode == 7)
        GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING_FALLING;
    else if (mode == 8)
        GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;

    if (pull == 0)
        GPIO_InitStruct.Pull = GPIO_NOPULL;
    else if (pull == 1)
        GPIO_InitStruct.Pull = GPIO_PULLUP;
    else if (pull == 2)
        GPIO_InitStruct.Pull = GPIO_PULLDOWN;

    if (speed == 0)
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    else if (speed == 1)
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_MEDIUM;
    else if (speed == 2)
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    else if (speed == 3)
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;

    if (alternate == 1)
        GPIO_InitStruct.Alternate = GPIO_AF7_USART1; // 复用
    else if (alternate == 2)
        GPIO_InitStruct.Alternate = GPIO_AF7_USART2; // 复用
    else if (alternate == 3)
        GPIO_InitStruct.Alternate = GPIO_AF7_USART3; // 复用
    else if (alternate == 4)
        GPIO_InitStruct.Alternate = GPIO_AF5_SPI1; // 复用
    else if (alternate == 5)
        GPIO_InitStruct.Alternate = GPIO_AF5_SPI2; // 复用
    else if (alternate == 6)
        GPIO_InitStruct.Alternate = GPIO_AF6_SPI3; // 复用
    else if (alternate == 32)
        GPIO_InitStruct.Alternate = GPIO_AF1_TIM1; // 复用
    else if (alternate == 33)
        GPIO_InitStruct.Alternate = GPIO_AF1_TIM2; // 复用
    else if (alternate == 34)
        GPIO_InitStruct.Alternate = GPIO_AF2_TIM3; // 复用
    else if (alternate == 35)
        GPIO_InitStruct.Alternate = GPIO_AF2_TIM4; // 复用
    else if (alternate == 36)
        GPIO_InitStruct.Alternate = GPIO_AF2_TIM5; // 复用
    else if (alternate == 37)
        GPIO_InitStruct.Alternate = GPIO_AF2_TIM5; // 复用6
    else if (alternate == 38)
        GPIO_InitStruct.Alternate = GPIO_AF2_TIM5; // 复用7
    else if (alternate == 39)
        GPIO_InitStruct.Alternate = GPIO_AF3_TIM8; // 复用
    else if (alternate == 40)
        GPIO_InitStruct.Alternate = GPIO_AF3_TIM9; // 复用
    else if (alternate == 41)
        GPIO_InitStruct.Alternate = GPIO_AF3_TIM10; // 复用
    else if (alternate == 42)
        GPIO_InitStruct.Alternate = GPIO_AF3_TIM11; // 复用
    else if (alternate == 43)
        GPIO_InitStruct.Alternate = GPIO_AF9_TIM12; // 复用
    else if (alternate == 44)
        GPIO_InitStruct.Alternate = GPIO_AF9_TIM13; // 复用
    else if (alternate == 45)
        GPIO_InitStruct.Alternate = GPIO_AF9_TIM14; // 复用

    HAL_GPIO_Init(config->port, &GPIO_InitStruct);
}

/**
 * @brief       设置GPIO引脚值
 * @param       pin       引脚号
 * @param       value     引脚值
 * @note        无
 * @retval      无
 */
void gpio_write_pin(uint8_t pin, uint8_t value)
{
    const gpio_config_t *config = &gpio_map[pin];
    HAL_GPIO_WritePin(config->port, config->pin, (GPIO_PinState)value);
}

/**
 * @brief       读取GPIO引脚值
 * @param       pin       引脚号
 * @note        无
 * @retval      引脚值
 */
uint8_t gpio_read_pin(uint8_t pin)
{
    const gpio_config_t *config = &gpio_map[pin];
    return HAL_GPIO_ReadPin(config->port, config->pin);
}

/**
 * @brief       切换GPIO引脚值
 * @param       pin       引脚号
 * @note        无
 * @retval      无
 */
void gpio_toggle_pin(uint8_t pin)
{
    const gpio_config_t *config = &gpio_map[pin];
    HAL_GPIO_TogglePin(config->port, config->pin);
}
