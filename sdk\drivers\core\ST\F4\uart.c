#include "./drivers/core/driver_core.h"
#include "./system/sys/sys.h"
#include "stdint.h"
static UART_HandleTypeDef uart1;
static UART_HandleTypeDef uart2;
static UART_HandleTypeDef uart3;
#define MAX_UART_NUM 3
void (*g_uart_recv_cbs[MAX_UART_NUM + 1])(uint8_t);

/**
 * @brief       初始化串口
 * @param       uart_id         串口号
 * @param       baudrate        波特率
 * @param       tx_pin          发送引脚号
 * @param       rx_pin          接收引脚号
 * @param       mode            引脚模式: 1:开启中断接收 0:不开启中断接收
 * @param       priority        中断优先级
 * @note        无
 * @retval      无
 */
void uart_init(uint8_t uart_id, uint32_t baudrate, uint8_t tx_pin, uint8_t rx_pin, uint8_t mode, uint8_t priority)
{
    UART_HandleTypeDef *uart = NULL;
    uint8_t alternate = 0;
    if (uart_id == 1)
    {
        uart = &uart1;
        uart->Instance = USART1;
        __HAL_RCC_USART1_CLK_ENABLE();
        alternate = AF_UART1;
    }
    else if (uart_id == 2)
    {
        uart = &uart2;
        uart->Instance = USART2;
        __HAL_RCC_USART2_CLK_ENABLE();
        alternate = AF_UART2;
    }
    else if (uart_id == 3)
    {
        uart = &uart3;
        uart->Instance = USART3;
        __HAL_RCC_USART3_CLK_ENABLE();
        alternate = AF_UART3;
    }
    uart->Init.BaudRate = baudrate;
    uart->Init.WordLength = UART_WORDLENGTH_8B;
    uart->Init.StopBits = UART_STOPBITS_1;
    uart->Init.Parity = UART_PARITY_NONE;
    uart->Init.Mode = UART_MODE_TX_RX;
    uart->Init.HwFlowCtl = UART_HWCONTROL_NONE;
    //USART1->CR1 |= (1 << 13) |(1 << 5); 
    //uart->Init.OverSampling = UART_OVERSAMPLING_16;

    HAL_UART_Init(uart);

    gpio_pin_init_alternate(tx_pin, AF_PP, NOPULL, VERY_HIGH, alternate);
    gpio_pin_init_alternate(rx_pin, AF_PP, PULLUP, VERY_HIGH, alternate);

    if (mode == 1)
    {
        if (uart_id == 1)
        {
            __HAL_UART_ENABLE_IT(&uart1, UART_IT_RXNE);
            HAL_NVIC_SetPriority(USART1_IRQn, 1, priority);
            HAL_NVIC_EnableIRQ(USART1_IRQn);
        }
        else if (uart_id == 2)
        {
            __HAL_UART_ENABLE_IT(&uart2, UART_IT_RXNE);
            HAL_NVIC_SetPriority(USART2_IRQn, 1, priority);
            HAL_NVIC_EnableIRQ(USART2_IRQn);
        }
        else if (uart_id == 3)
        {
            __HAL_UART_ENABLE_IT(&uart3, UART_IT_RXNE);
            HAL_NVIC_SetPriority(USART3_IRQn, 1, priority);
            HAL_NVIC_EnableIRQ(USART3_IRQn);
        }
    }
}

/**
 * @brief       发送数据
 * @param       uart_id       串口号
 * @param       data       发送数据
 * @param       len       发送数据长度
 * @param       timeout       超时时间
 * @note        无
 * @retval      无
 */
void uart_send_data(uint8_t uart_id, uint8_t *data, uint16_t len, uint32_t timeout)
{
    if (uart_id == 1)
    {
        HAL_UART_Transmit(&uart1, data, len, timeout);
    }
    else if (uart_id == 2)
    {
        HAL_UART_Transmit(&uart2, data, len, timeout);
    }
    else if (uart_id == 3)
    {
        HAL_UART_Transmit(&uart3, data, len, timeout);
    }
}

/**
 * @brief       发送数据
 * @param       uart_id       串口号
 * @param       callback       接收回调函数
 * @note        无
 * @retval      无
 */
void uart_add_recv_callback(uint8_t uart_id, void (*callback)(uint8_t))
{
    g_uart_recv_cbs[uart_id] = callback;
}

void USART1_IRQHandler(void)
{
    if (USART1->SR & USART_SR_RXNE)
    {
        uint8_t ch = USART1->DR;
        g_uart_recv_cbs[1](ch);
    }
}

void USART2_IRQHandler(void)
{
    if (USART2->SR & USART_SR_RXNE)
    {
        uint8_t ch = USART2->DR;
        g_uart_recv_cbs[2](ch);
    }
}

void USART3_IRQHandler(void)
{
    if (USART3->SR & USART_SR_RXNE)
    {
        uint8_t ch = USART3->DR;
        g_uart_recv_cbs[3](ch);
    }
}
