#include <errno.h>
#include <string.h>
#include "device.h"

extern struct device Image$$DEVEICE_DRIVER_CORE_REGISTRY$$Base[];
extern struct device Image$$DEVEICE_DRIVER_CORE_REGISTRY$$Limit[];
extern struct device Image$$DEVEICE_DRIVER_REGISTRY$$Base[];
extern struct device Image$$DEVEICE_DRIVER_REGISTRY$$Limit[];
extern struct device Image$$DEVEICE_MIDDWARE_REGISTRY$$Base[];
extern struct device Image$$DEVEICE_MIDDWARE_REGISTRY$$Limit[];
extern struct device Image$$DEVEICE_APPLICATION_REGISTRY$$Base[];
extern struct device Image$$DEVEICE_APPLICATION_REGISTRY$$Limit[];

static struct device *config_levels_start[] = {
    Image$$DEVEICE_DRIVER_CORE_REGISTRY$$Base,
    Image$$DEVEICE_DRIVER_REGISTRY$$Base,
    Image$$DEVEICE_MIDDWARE_REGISTRY$$Base,
	Image$$DEVEICE_APPLICATION_REGISTRY$$Base,
};

static struct device *config_levels_end[] = {
    Image$$DEVEICE_DRIVER_CORE_REGISTRY$$Limit,
    Image$$DEVEICE_DRIVER_REGISTRY$$Limit,
    Image$$DEVEICE_MIDDWARE_REGISTRY$$Limit,
	Image$$DEVEICE_APPLICATION_REGISTRY$$Limit,
};

void _sys_device_do_config_level(int level)
{
	struct device *info;

	for (info = config_levels_start[level]; info < config_levels_end[level];
								info++) {
		struct device_config *device = (struct device_config *)info->config;

		device->init(info);
	}
}

struct device *device_get_binding(const char *name, unsigned char level)
{
	struct device *info;

	for (info = config_levels_start[level]; info < config_levels_end[level]; info++) {
		if (info->driver_api && !strcmp(name, info->config->name)) {
			return info;
		}
	}
    return NULL;
}

// void device_init(void)
// {
//     int level;

//     for (level = 0; level < ARRAY_SIZE(config_levels) - 1; level++) {
//         _sys_device_do_config_level(level);
//     }
// }
