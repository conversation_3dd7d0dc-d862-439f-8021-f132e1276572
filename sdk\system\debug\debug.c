#include "stdint.h"
#include "string.h"
#include "stdarg.h"
#include "stdio.h"

#include "./system/sys/sys.h"
#include "./system/debug/debug.h"
#include "./drivers/core/driver_core.h"

static char log_str[1024] = {0};
static void debug_recv_callback(uint8_t debug_recv_data);
char debug_time_str[32] = {0};
char *get_debug_time(void)
{
    uint32_t debug_time = sys_get_tick();
    uint16_t msec = debug_time % 1000;
    debug_time /= 1000;
    uint8_t sec = debug_time % 60;
    debug_time /= 60;
    uint8_t min = debug_time % 60;
    debug_time /= 60;
    uint8_t hour = debug_time;

    snprintf(debug_time_str, sizeof(debug_time_str), "[%02d:%02d:%02d.%03d]", hour, min, sec, msec);
    return debug_time_str;
}

void debug_send_data(uint8_t *data, uint16_t length)
{
    uart_send_data(1, data, length, 100);
}

void debug_init(void)
{
    uart_init(1, 1000000, PA9, PA10, 1, 1);
    uart_add_recv_callback(1, debug_recv_callback);
}

void debug_printf(char *format, ...) // 存在资源竞争
{
    va_list arg;
    va_start(arg, format);
    snprintf(log_str, sizeof(log_str), "%s ", get_debug_time());
    //vsprintf(log_str, format, arg);
    vsnprintf(log_str + strlen(log_str), sizeof(log_str) - strlen(log_str), format, arg);
    va_end(arg);
    debug_send_data((uint8_t *)log_str, strlen(log_str));
}

void debug_error(const char *TAG, char *format, ...)
{
    va_list arg;
    va_start(arg, format);

    // 打印错误前缀和调用函数名
    snprintf(log_str, sizeof(log_str), "%s[ERROR][%s] ", get_debug_time(), TAG);
    vsnprintf(log_str + strlen(log_str), sizeof(log_str) - strlen(log_str), format, arg);
    log_str[sizeof(log_str) - 1] = '\0'; // 确保字符串以空字符结尾

    va_end(arg);
    debug_send_data((uint8_t *)log_str, strlen(log_str));
}

void debug_warning(const char *TAG, char *format, ...)
{
    va_list arg;
    va_start(arg, format);

    // 打印消息前缀和调用函数名
    snprintf(log_str, sizeof(log_str), "%s[WARNING][%s] ", get_debug_time(), TAG);
    vsnprintf(log_str + strlen(log_str), sizeof(log_str) - strlen(log_str), format, arg);
    log_str[sizeof(log_str) - 1] = '\0'; // 确保字符串以空字符结尾

    va_end(arg);
    debug_send_data((uint8_t *)log_str, strlen(log_str));
}

void debug_message(const char *TAG, char *format, ...)
{
    va_list arg;
    va_start(arg, format);

    // 打印消息前缀和调用函数名
    snprintf(log_str, sizeof(log_str), "%s[INFO][%s] ", get_debug_time(), TAG);
    vsnprintf(log_str + strlen(log_str), sizeof(log_str) - strlen(log_str), format, arg);
    log_str[sizeof(log_str) - 1] = '\0'; // 确保字符串以空字符结尾

    va_end(arg);
    debug_send_data((uint8_t *)log_str, strlen(log_str));
}

void debug_test(void)
{

    debug_printf("这是一条普通日志\r\n");

    debug_error("A", "发生错误code:%d\r\n", -1);
    debug_warning("B", "发生警告code:%d\r\n", 666);
    debug_message("C", "这是一条带有INFO标签的消息:%s\r\n", "测试消息");

    DEBUG_ERROR("发生错误code:%d\r\n", -1);
    DEBUG_WARN("发生警告code:%d\r\n", 666);
    DEBUG_INFO("这是一条带有INFO标签的消息:%s\r\n", "测试消息");
}

static uint8_t debug_recv_buf[128] = {0};
static uint16_t debug_recv_len = 0;

static void debug_recv_callback(uint8_t debug_recv_data)
{
    debug_recv_buf[debug_recv_len++] = debug_recv_data;
    if (debug_recv_len >= sizeof(debug_recv_buf))
    {
        debug_recv_len = 0;
    }
    if (debug_recv_buf[debug_recv_len - 2] == '\r' && debug_recv_buf[debug_recv_len - 1] == '\n')
    {
        DEBUG_INFO("%s", debug_recv_buf);
        memset(debug_recv_buf, 0, sizeof(debug_recv_buf));
        debug_recv_len = 0;
    }
}
