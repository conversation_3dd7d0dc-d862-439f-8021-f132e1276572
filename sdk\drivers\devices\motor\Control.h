#ifndef __Control_H
#define __Control_H

#include "device.h" // Device header
#include "Encoder.h"
#include "PID.h"

/* 减速电机减速比 */
#define Reduction_ratio 28

/* 编码器物理分辨率 */ // 即编码器线数或者叫精度
#define ENCODER_RESOLUTION 500

/* 经过倍频之后的总分辨率 */
#define Frequency_Divide (ENCODER_RESOLUTION * 4) /* 4倍频后的总分辨率 */

#define SPEED_PID_PERIOD 20 // 这个要看定时器的中断周期

#define LUN_JU 10.5 // 单位cm
#define WheelR 2.4	// 一圈15.1cm

#define TARGET_SPEED_MAX 5000 //// 60rpm可以3s走完60cm

/* 小车方向控制枚举 */
typedef enum
{
	left_90,
	right_90,
	back_180
} spin_dir_t;

extern float LeftSpeed_Out, LeftLocation_Out;
extern float RightSpeed_Out, RightLocation_Out;

extern float Right_current_Speed, Left_current_Speed;
extern float Right_current_cm, Left_current_cm;

void car_go(int32_t target_location_cm);
void spin_turn(spin_dir_t zhuanxiang);
void Location_Speed_control(void);

float Right_location_pid_control(void);
float Right_speed_pid_control(void);
float Left_location_pid_control(void);
float Left_speed_pid_control(void);

#endif
