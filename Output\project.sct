; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 0x08000000 0x00100000  {    ; load region size_region
  ER_IROM1 0x08000000 0x00100000  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY(.devconfig.init)
   .ANY (+RO)
   .ANY (+XO)
  }
  
  DEVEICE_DRIVER_CORE_REGISTRY 0x20000000 0x0000100{
	.ANY (.device_DRIVER_CORE)
  }
  DEVEICE_DRIVER_REGISTRY 0x20000100 0x0000100{
	.ANY (.device_DRIVER)
  }
  
  DEVEICE_MIDDWARE_REGISTRY 0x20000200 0x0000100{
	.ANY (.device_MIDDWARE)
  }
  
  DEVEICE_APPLICATION_REGISTRY 0x20000300 0x0000100{
	.ANY (.device_APPLICATION)
  }
  
  
  RW_IRAM1 0x20000400 0x0001FC00  {  ; RW data
   .ANY (+RW +ZI)
  }
}

