#ifndef __DEBUG_H__
#define __DEBUG_H__

// 使用宏来自动添加函数名前缀
#define DEBUG_ERROR(format, ...) debug_error(__func__, format, ##__VA_ARGS__)
#define DEBUG_INFO(format, ...) debug_message(__func__, format, ##__VA_ARGS__)
#define DEBUG_WARN(format, ...) debug_warning(__func__, format, ##__VA_ARGS__)
#pragma diag_suppress 870 // 忽略中文字符串编译警告

void debug_init(void);
void debug_printf(char *format, ...);
void debug_error(const char *TAG, char *format, ...);
void debug_message(const char *TAG, char *format, ...);
void debug_warning(const char *TAG, char *format, ...);

void debug_test(void); // 测试函数

#endif
