#ifndef __BOARD_CONFIG_H__
#define __BOARD_CONFIG_H__

#include "../../sdk/system/sys/sys.h"

#define TCFG_ENABLE 1
#define TCFG_DISABLE 0

/**********主控芯片配置************/
#define TCFG_MCU_STM32F1 1
#define TCFG_MCU_STM32F4 2
#define TCFG_MCU_TIMSP430 3
#define TCFG_MCU_CHIP TCFG_MCU_STM32F4
#include "./drivers/core/driver_core.h"

/**********操作系统配置配置************/
#define TCFG_OS_FREE_RTOS 1
#define TCFG_OS_RTT 2
#define TCFG_OS TCFG_OS_FREE_RTOS

/**********GPIO 按键************/
#define TCFG_IO_KEY_EN TCFG_DISABLE
#define TCFG_IO_KEY_USER_MAX 4
#define TCFG_IO_KEY_PIN PB12, PB13, PB14, PB15
#define TCFG_IO_KEY_VALUE 1, 2, 3, 4
#define TCFG_IO_KEY_CLICK_STATUS 1, 0, 0, 0

/**********ADC 按键************/
#define TCFG_ADC_KEY_EN TCFG_DISABLE

#define TCFG_ADC_KEY_NUM 4
#define TCFG_ADC_KEY_PIN PA0
#define TCFG_ADC_KEY_ADC_ID 1
#define TCFG_ADC_KEY_CHANNEL NULL

#define TCFG_ADC_KEY_VDDIO (0xfffL) // 4095
#define TCFG_ADC_KEY_VALUE_MAX 4095
#define TCFG_ADC_KEY_R_UP 100
#define TCFG_ADC_KEY_R0 0
#define TCFG_ADC_KEY_R1 62
#define TCFG_ADC_KEY_R2 150
#define TCFG_ADC_KEY_R3 240

#define TCFG_ADC_KEY_ADC_0 (TCFG_ADC_KEY_VDDIO * TCFG_ADC_KEY_R0 / (TCFG_ADC_KEY_R0 + TCFG_ADC_KEY_R_UP)) // 0.0R       0
#define TCFG_ADC_KEY_ADC_1 (TCFG_ADC_KEY_VDDIO * TCFG_ADC_KEY_R1 / (TCFG_ADC_KEY_R1 + TCFG_ADC_KEY_R_UP)) // 6.2k       1567
#define TCFG_ADC_KEY_ADC_2 (TCFG_ADC_KEY_VDDIO * TCFG_ADC_KEY_R2 / (TCFG_ADC_KEY_R2 + TCFG_ADC_KEY_R_UP)) // 15.0k      2457
#define TCFG_ADC_KEY_ADC_3 (TCFG_ADC_KEY_VDDIO * TCFG_ADC_KEY_R3 / (TCFG_ADC_KEY_R3 + TCFG_ADC_KEY_R_UP)) // 24.0k      2890

#define TCFG_ADC_KEY_VOLTAGE0 ((TCFG_ADC_KEY_ADC_0 + TCFG_ADC_KEY_ADC_1) / 2) // 783
#define TCFG_ADC_KEY_VOLTAGE1 ((TCFG_ADC_KEY_ADC_1 + TCFG_ADC_KEY_ADC_2) / 2) // 2012
#define TCFG_ADC_KEY_VOLTAGE2 ((TCFG_ADC_KEY_ADC_2 + TCFG_ADC_KEY_ADC_3) / 2) // 2673
#define TCFG_ADC_KEY_VOLTAGE3 ((TCFG_ADC_KEY_ADC_3 + TCFG_ADC_KEY_VDDIO) / 2) // 3492

#define TCFG_ADC_KEY_VALUE0 0
#define TCFG_ADC_KEY_VALUE1 1
#define TCFG_ADC_KEY_VALUE2 2
#define TCFG_ADC_KEY_VALUE3 3

/**********I2C  软件配置************/
#define TCFG_I2C_EN TCFG_ENABLE
#define TCFG_I2C_NUM 3

/**********display显示配置************/
#define TCFG_DISPLAY_EN TCFG_ENABLE

#define TCFG_DISPLAY_OLED_I2C 1
#define TCFG_DISPLAY_OLED_SPI 2
#define TCFG_DISPLAY_LCD_SPI 3

#define TCFG_DISPLAY_TYPE TCFG_DISPLAY_OLED_SPI
#define TCFG_DISPLAY_OLED_I2C_ID 1
#define TCFG_DISPLAY_OLED_I2C_SDA PB14
#define TCFG_DISPLAY_OLED_I2C_SCL PB15

#define TCFG_DISPLAY_OLED_SPI_ID 1
#define TCFG_DISPLAY_OLED_SPI_SPEED SPI_SPEED_3
#define TCFG_DISPLAY_OLED_SPI_SCK PA5
#define TCFG_DISPLAY_OLED_SPI_MOSI PA7
#define TCFG_DISPLAY_OLED_SPI_CS PA1
#define TCFG_DISPLAY_OLED_SPI_DC PA2
#define TCFG_DISPLAY_OLED_SPI_RST PA3

#endif
