#ifndef __SYSTEM_EVENT_H__
#define __SYSTEM_EVENT_H__

#include "stdint.h"

#define SYS_ALL_EVENT 0xffff
#define SYS_KEY_EVENT 0x0001

#define DEVICE_EVENT_FROM_KEY (('K' << 24) | ('E' << 16) | ('Y' << 8) | '\0')

struct key_event
{
    uint8_t init;
    uint8_t type;
    uint16_t event;
    uint32_t value;
    uint32_t tmr;
};

struct sys_event
{
    uint16_t type;
    uint8_t consumed;
    void *arg;
    union
    {
        struct key_event key;
    } u;
};

void sys_event_init(void);
void sys_event_send_notify(struct sys_event *e);
void sys_event_get_notify(struct sys_event *e);

#endif
