# SDK 说明以及规范

## 说明

此 SDK 是实验室内部共同开发的成果，具有私有性质，未经授权不得外传。

## SDK 文件目录说明

  * **Document** ：用于存放与 SDK 相关的各类文档，方便开发人员查阅和了解 SDK 的详细信息。
  * **Driver-Core** ：存放芯片的驱动代码，这是 SDK 中与硬件交互的核心部分，为上层应用提供硬件操作的基础功能。
  * **MDK-ARM** ：包含使用 MDK keil5 工程相关的文件，开发者在该开发环境下进行项目搭建和调试。
  * **Output** ：用于存放编译后生成的文件，如可执行文件、库文件等。
  * **sdk** ：专门用于存放有关此 SDK 的文件，如配置文件、工具脚本等，以保证 SDK 的完整性和易用性。
  * **tools** ：存放一些辅助开发的工具，这些工具可以提高开发效率，如代码生成工具、调试工具等。

## 其他说明

  1. **SDK 功能说明**
关于 SDK 的详细功能说明，开发人员可以参考文档《SDK 功能说明》。这份文档对 SDK 提供的各项功能进行了全面而深入的介绍，包括功能模块的划分、每个模块的具体功能描述、功能调用的示例代码以及相关的注意事项等，帮助开发人员更好地理解和使用 SDK，从而提高开发效率和项目的成功率。
  2. **更换芯片适配说明**
如果需要更换 SDK 适配的芯片，开发人员可以参考文档《更换芯片适配指南》其中详细介绍了更换芯片时需要考虑的因素、步骤以及相关的注意事项，以确保 SDK 能够顺利地在新的芯片平台上运行。
  3. **添加驱动规范**
当需要向 SDK 中添加新的驱动时，开发人员应参考文档《驱动开发规范》。该文档规定了驱动开发的标准流程、代码编写规范、接口设计要求等内容，旨在保证新增驱动的质量和兼容性，使其能够与现有的 SDK 无缝集成并稳定运行。