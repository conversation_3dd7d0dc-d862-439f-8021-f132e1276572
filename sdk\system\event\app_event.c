#include "./system/sys/sys.h"

#include "./middleware/rtos/os_abstract.h"

#include "./system/event/app_event.h"
#include "./system/event/app_msg.h"
#include "./system/debug/debug.h"
#include "./drivers/core/driver_core.h"

static os_queue_handle_t app_msg_queue = NULL;
static os_task_handle_t app_msg_task = NULL;
static void app_msg_handle(uint16_t msg);
static void app_process_msg(void *arg);
void app_event_init(void)
{
    app_msg_queue = os_queue_create(10, sizeof(uint16_t)); // 创建一个队列，用于存放系统事件
    os_task_create(app_process_msg, "sys_event_task222", 128 * 1, NULL, 3, &app_msg_task);
}

void app_msg_send_notify(uint16_t msg)
{
    os_queue_send(app_msg_queue, &msg, 0); // 将系统事件放入队列中
}

uint16_t app_msg_get_notify(void)
{
    uint16_t msg;
    os_queue_receive(app_msg_queue, &msg, os_wait_max); // 从队列中取出系统事件
    return msg;
}

static void app_process_msg(void *arg)
{
    uint16_t msg;
    while (1)
    {
        msg = app_msg_get_notify();
        app_msg_handle(msg);
    }
}

// app事件，执行相应的操作
static void app_msg_handle(uint16_t msg)
{
    DEBUG_INFO("msg:%d\r\n", msg);
    switch (msg)
    {
    case MSG_RESET:
        debug_printf("MSG_RESET\r\n");
        break;
    case MSG_POWER_ON:
        debug_printf("MSG_POWER_ON\r\n");
        gpio_write_pin(PB2, 1);
        break;
    case MSG_POWER_OFF:
        debug_printf("MSG_POWER_OFF\r\n");
        gpio_write_pin(PB2, 0);
        break;
    default:
        DEBUG_WARN("unknow msg:%d\r\n", msg);
        break;
    }
}
