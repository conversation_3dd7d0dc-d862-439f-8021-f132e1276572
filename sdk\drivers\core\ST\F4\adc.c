#include "./drivers/core/driver_core.h"
#include "./system/sys/sys.h"
#include "./middleware/rtos/os_abstract.h"
#include "./system/debug/debug.h"

static uint8_t adc_pin_value_map[16] = {0};
static uint16_t adc_value[16] = {0};
static uint8_t adc_regular_rank_cnt = 0;
static ADC_HandleTypeDef adc1;
static ADC_HandleTypeDef adc2;
static ADC_HandleTypeDef adc3;
static uint8_t is_adc_task_start = 0;
static uint8_t is_adc1_start = 0;
//static uint8_t is_adc2_start = 0;
//static uint8_t is_adc3_start = 0;

#define ADC_CHANNEL_N 0xFFFF

static const uint16_t adc_channel_map[] =
    {
        ADC_CHANNEL_0, ADC_CHANNEL_1, ADC_CHANNEL_2, ADC_CHANNEL_3, ADC_CHANNEL_4, ADC_CHANNEL_5, ADC_CHANNEL_6, ADC_CHANNEL_7,
        ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, // PA0-PA15
        ADC_CHANNEL_8, ADC_CHANNEL_9, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N,
        ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, // PB0-PB15
        ADC_CHANNEL_10, ADC_CHANNEL_11, ADC_CHANNEL_12, ADC_CHANNEL_13, ADC_CHANNEL_14, ADC_CHANNEL_15, ADC_CHANNEL_N, ADC_CHANNEL_N,
        ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, // PC0-PC15
        ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N,
        ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, // PD0-PD15
        ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N,
        ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, // PE0-PE15
        ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N,
        ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, // PF0-PF15
        ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N,
        ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, ADC_CHANNEL_N, // PG0-PG15
        ADC_CHANNEL_16,                                                                                                         // 内部温度传感器
};

static void adc_read_task(void *arg);

void adc_pin_init(uint8_t adc_id, uint8_t pin, uint8_t channel, uint8_t rank) // 暂时只支持adc1和adc2,后续优化成连续扫描,目前是手动触发单次扫描
{
    adc_pin_value_map[adc_regular_rank_cnt] = pin;
    ADC_ChannelConfTypeDef sConfig = {0};
    sConfig.Channel = adc_channel_map[pin];
    if (sConfig.Channel == ADC_CHANNEL_N)
    {
        DEBUG_ERROR("adc pin[%d] not support", pin);
        return;
    }
    sConfig.SamplingTime = ADC_SAMPLETIME_144CYCLES;
    sConfig.Rank = ++adc_regular_rank_cnt;
    ADC_HandleTypeDef *adcx = NULL;
    if (adc_id == 1)
    {
        __HAL_RCC_ADC1_CLK_ENABLE();
        adcx = &adc1;
        adcx->Instance = ADC1;
        HAL_ADC_ConfigChannel(adcx, &sConfig);
        is_adc1_start = 1;
    }
    else if (adc_id == 2)
    {
        __HAL_RCC_ADC2_CLK_ENABLE();
        adcx = &adc2;
        adcx->Instance = ADC2;
        HAL_ADC_ConfigChannel(adcx, &sConfig);
        //is_adc2_start = 1;
    }
    else if (adc_id == 3)
    {
        __HAL_RCC_ADC3_CLK_ENABLE();
        adcx = &adc3;
        adcx->Instance = ADC3;
        HAL_ADC_ConfigChannel(adcx, &sConfig);
        //is_adc3_start = 1;
    }

    adcx->Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV8;
    adcx->Init.Resolution = ADC_RESOLUTION_12B;
    adcx->Init.ScanConvMode = ENABLE;
    adcx->Init.ContinuousConvMode = DISABLE;
    adcx->Init.DiscontinuousConvMode = ENABLE;
    adcx->Init.NbrOfDiscConversion = 1;
    adcx->Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
    adcx->Init.ExternalTrigConv = ADC_SOFTWARE_START;
    adcx->Init.DataAlign = ADC_DATAALIGN_RIGHT;
    adcx->Init.NbrOfConversion = adc_regular_rank_cnt;
    adcx->Init.DMAContinuousRequests = DISABLE;
    adcx->Init.EOCSelection = ADC_EOC_SINGLE_CONV;

    HAL_ADC_Init(adcx);

    gpio_pin_init(pin, ANALOG, NOPULL, HIGH);

    if (is_adc_task_start == 0)
    {
        is_adc_task_start = 1;
        os_task_create(adc_read_task, "adc_read_task", 128, NULL, 3, NULL);
    }
}

static void adc_read_task(void *arg)
{
    while (1)
    {
        ADC_HandleTypeDef *adcx = NULL;

        if (is_adc1_start == 1)
        {
            adcx = &adc1;
            for (uint8_t i = 0; i < adc_regular_rank_cnt; i++)
            {
                HAL_ADC_Start(adcx);
                HAL_ADC_PollForConversion(adcx, 50);
                adc_value[i] = HAL_ADC_GetValue(adcx);
                // DEBUG_INFO("adc[%d] value[%d]:%d\r\n", 1, i, adc_value[i]);
            }
            HAL_ADC_Stop(adcx);
        }

        os_task_delay(10);
    }
}

uint16_t adc_get_value(uint8_t pin)
{
    for (uint8_t i = 0; i < adc_regular_rank_cnt; i++)
    {
        if (adc_pin_value_map[i] == pin)
            return adc_value[i];
    }
    return 0;
}
