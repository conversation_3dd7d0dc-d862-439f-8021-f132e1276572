#include "./config/board_config.h"
#include "./drivers/devices/input/key/gpio_key/gpio_key.h"
#include "./drivers/devices/input/key/adc_key/adc_key.h"

#if TCFG_IO_KEY_EN == TCFG_ENABLE

const gpio_key_platform_data_t gpio_key_data = {
    .key_value = {TCFG_IO_KEY_VALUE},
    .pin = {TCFG_IO_KEY_PIN},
    .click_status = {TCFG_IO_KEY_CLICK_STATUS},
};

#endif

#if TCFG_ADC_KEY_EN == TCFG_ENABLE
const adc_key_platform_data_t adc_key_data = {

    .adc_key_pin = TCFG_ADC_KEY_PIN, // AD按键对应引脚
    .adc_value = {
        TCFG_ADC_KEY_VOLTAGE0,
        TCFG_ADC_KEY_VOLTAGE1,
        TCFG_ADC_KEY_VOLTAGE2,
        TCFG_ADC_KEY_VOLTAGE3,
    },
    .key_value = {
        TCFG_ADC_KEY_VALUE0,
        TCFG_ADC_KEY_VALUE1,
        TCFG_ADC_KEY_VALUE2,
        TCFG_ADC_KEY_VALUE3,
    },
};

#endif
