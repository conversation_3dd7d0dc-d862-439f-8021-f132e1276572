#ifndef __SPI_H__
#define __SPI_H__

#define SPI_SPEED_0 0   // 256分频
#define SPI_SPEED_1 1   // 128分频
#define SPI_SPEED_2 2   // 64分频
#define SPI_SPEED_3 3  // 32分频
#define SPI_SPEED_4 4  // 16分频
#define SPI_SPEED_5 5  // 8分频
#define SPI_SPEED_6 6 // 4分频
#define SPI_SPEED_7 7 // 2分频

#include "stdint.h"

/**
 * @brief       SPI引脚初始化
 * @param       spi_id       SPI ID号
 * @param       mode         SPI模式: 0-全双工主模式 1-全双工从模式 2-半双工主机 3-半双工从机 4-作为主机只接受 5-作为从机只接受 6-作为主机只发送 7-作为从机只发送
 * @param       speed        SPI速度:具体参数见spi.h  0-7 数组越高,速度越快
 * @param       mosi         MOSI引脚
 * @param       miso         MISO引脚
 * @param       sck          SCK引脚
 * @note        无
 * @retval      无
 */
void spi_pin_init(uint8_t spi_id, uint8_t mode, uint8_t speed, uint8_t mosi, uint8_t miso, uint8_t sck);

/**
 * @brief       SPI发送数据
 * @param       spi_id       SPI ID号
 * @param       data         发送数据
 * @param       size         发送数据大小
 * @param       timeout      超时时间
 * @note        无
 * @retval      无
 */
uint8_t spi_send_data(uint8_t spi_id, uint8_t *data, uint16_t size, uint32_t timeout);

/**
 * @brief       SPI接收数据
 * @param       spi_id       SPI ID号
 * @param       data         接收数据
 * @param       size         接收数据大小
 * @param       timeout      超时时间
 * @note        无
 * @retval      无
 */
uint8_t spi_receive_data(uint8_t spi_id, uint8_t *data, uint16_t size, uint32_t timeout);

#endif /* __SPI_H__ */
