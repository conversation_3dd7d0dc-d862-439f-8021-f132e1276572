#include "./drivers/core/driver_core.h"
#include "./system/sys/sys.h"

SPI_HandleTypeDef spi1;
SPI_HandleTypeDef spi2;
SPI_HandleTypeDef spi3;

/**
 * @brief       SPI引脚初始化
 * @param       spi_id       SPI ID号
 * @param       mode         SPI模式: 0-全双工主模式 1-全双工从模式 2-半双工主机 3-半双工从机 4-作为主机只接受 5-作为从机只接受 6-作为主机只发送 7-作为从机只发送
 * @param       speed        SPI速度:具体参数见spi.h  0-7 数组越高,速度越快
 * @param       mosi         MOSI引脚
 * @param       miso         MISO引脚
 * @param       sck          SCK引脚
 * @note        无
 * @retval      无
 */
void spi_pin_init(uint8_t spi_id, uint8_t mode, uint8_t speed, uint8_t mosi, uint8_t miso, uint8_t sck)
{
    SPI_HandleTypeDef *spi;
    uint8_t alternate = 0;
    if (spi_id == 1)
    {
        __HAL_RCC_SPI1_CLK_ENABLE();
        spi = &spi1;
        spi->Instance = SPI1;
        alternate = 4;
    }
    else if (spi_id == 2)
    {
        __HAL_RCC_SPI2_CLK_ENABLE();
        spi = &spi2;
        spi->Instance = SPI2;
        alternate = 5;
    }
    else if (spi_id == 3)
    {
        __HAL_RCC_SPI3_CLK_ENABLE();
        spi = &spi3;
        spi->Instance = SPI3;
        alternate = 6;
    }

    if (mode == 0) // 全双工主模式
    {
        spi->Init.Mode = SPI_MODE_MASTER;
        spi->Init.Direction = SPI_DIRECTION_2LINES;
    }
    else if (mode == 1) // 全双工从模式
    {
        spi->Init.Mode = SPI_MODE_SLAVE;
        spi->Init.Direction = SPI_DIRECTION_2LINES;
    }
    else if (mode == 2) // 半双工主机
    {
        spi->Init.Mode = SPI_MODE_MASTER;
        spi->Init.Direction = SPI_DIRECTION_1LINE;
    }
    else if (mode == 3) // 半双工从机
    {
        spi->Init.Mode = SPI_MODE_SLAVE;
        spi->Init.Direction = SPI_DIRECTION_1LINE;
    }
    else if (mode == 4) // 作为主机只接受
    {
        spi->Init.Mode = SPI_MODE_MASTER;
        spi->Init.Direction = SPI_DIRECTION_2LINES_RXONLY;
    }
    else if (mode == 5) // 作为从机只接受
    {
        spi->Init.Mode = SPI_MODE_SLAVE;
        spi->Init.Direction = SPI_DIRECTION_2LINES_RXONLY;
    }
    else if (mode == 6) // 作为主机只发送
    {
        spi->Init.Mode = SPI_MODE_MASTER;
        spi->Init.Direction = SPI_DIRECTION_2LINES;
    }
    else if (mode == 7) // 作为从机只发送
    {
        spi->Init.Mode = SPI_MODE_SLAVE;
        spi->Init.Direction = SPI_DIRECTION_2LINES;
    }

    spi->Init.DataSize = SPI_DATASIZE_8BIT;   /* 设置SPI的数据大小:SPI发送接收8位帧结构 */
    spi->Init.CLKPolarity = SPI_POLARITY_LOW; /* 串行同步时钟的空闲状态为高电平 */
    spi->Init.CLKPhase = SPI_PHASE_1EDGE;     /* 串行同步时钟的第二个跳变沿（上升或下降）数据被采样 */
    spi->Init.NSS = SPI_NSS_SOFT;             /* NSS信号由硬件（NSS管脚）还是软件（使用SSI位）管理:内部NSS信号有SSI位控制 */

    spi->Init.FirstBit = SPI_FIRSTBIT_MSB;                 /* 指定数据传输从MSB位还是LSB位开始:数据传输从MSB位开始 */
    spi->Init.TIMode = SPI_TIMODE_DISABLE;                 /* 关闭TI模式 */
    spi->Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE; /* 关闭硬件CRC校验 */
    spi->Init.CRCPolynomial = 10;                          /* CRC值计算的多项式 */

    if (speed == 7)
        spi->Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_2; /* 定义波特率预分频的值:波特率预分频值为2 */
    else if (speed == 6)
        spi->Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_4; /* 定义波特率预分频的值:波特率预分频值为4 */
    else if (speed == 5)
        spi->Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_8; /* 定义波特率预分频的值:波特率预分频值为8 */
    else if (speed == 4)
        spi->Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_16; /* 定义波特率预分频的值:波特率预分频值为16 */
    else if (speed == 3)
        spi->Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_32; /* 定义波特率预分频的值:波特率预分频值为32 */
    else if (speed == 2)
        spi->Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_64; /* 定义波特率预分频的值:波特率预分频值为64 */
    else if (speed == 1)
        spi->Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_128; /* 定义波特率预分频的值:波特率预分频值为128 */
    else if (speed == 0)
        spi->Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_256; /* 定义波特率预分频的值:波特率预分频值为256 */

    HAL_SPI_Init(spi); /* 初始化 */
    __HAL_SPI_ENABLE(spi);
    if (mode == 0 || mode == 1 || mode == 2 || mode == 5 || mode == 6)
        gpio_pin_init_alternate(mosi, AF_PP, PULLUP, HIGH, alternate);
    if (mode == 0 || mode == 1 || mode == 3 || mode == 4 || mode == 7)
        gpio_pin_init_alternate(miso, AF_PP, PULLUP, HIGH, alternate);
    gpio_pin_init_alternate(sck, AF_PP, PULLUP, HIGH, alternate);
}

/**
 * @brief       SPI发送数据
 * @param       spi_id       SPI ID号
 * @param       data         发送数据
 * @param       size         发送数据大小
 * @param       timeout      超时时间
 * @note        无
 * @retval      无
 */
uint8_t spi_send_data(uint8_t spi_id, uint8_t *data, uint16_t size, uint32_t timeout)
{
    SPI_HandleTypeDef *spi;
    uint8_t ret = 0;
    if (spi_id == 1)
        spi = &spi1;
    else if (spi_id == 2)
        spi = &spi2;
    else if (spi_id == 3)
        spi = &spi3;

    ret = HAL_SPI_Transmit(spi, data, size, timeout);
    return ret;
}

/**
 * @brief       SPI接收数据
 * @param       spi_id       SPI ID号
 * @param       data         接收数据
 * @param       size         接收数据大小
 * @param       timeout      超时时间
 * @note        无
 * @retval      无
 */
uint8_t spi_receive_data(uint8_t spi_id, uint8_t *data, uint16_t size, uint32_t timeout)
{
    SPI_HandleTypeDef *spi;
    uint8_t ret = 0;
    if (spi_id == 1)
        spi = &spi1;
    else if (spi_id == 2)
        spi = &spi2;
    else if (spi_id == 3)
        spi = &spi3;

    ret = HAL_SPI_Receive(spi, data, size, timeout);
    return ret;
}
