{"files.associations": {"os_abstract.h": "c", "sdk_main.h": "c", "sys.h": "c", "gpio.h": "c", "hal_gpio.h": "c", "stm32f4xx_hal.h": "c", "stm32f4xx_hal_def.h": "c", "uart.h": "c", "debug.h": "c", "driver_core.h": "c", "adc.h": "c", "key.h": "c", "board_config.h": "c", "gpio_key.h": "c", "adc_key.h": "c", "system_event.h": "c", "app_event.h": "c", "wdg.h": "c", "stm32f4xx.h": "c", "freertos.h": "c", "ssd1306.h": "c", "driver_oled.h": "c", "string.h": "c", "stdint.h": "c", "typeinfo": "c", "bit": "c", "compare": "c", "cstdlib": "c", "cmath": "c", "new": "c", "type_traits": "c", "device.h": "c", "limits": "c", "cstdint": "c", "gpio_led.h": "c", "cctype": "c", "cfloat": "c", "climits": "c", "cstdarg": "c", "cstdio": "c", "spi.h": "c", "tim.h": "c", "motor.h": "c", "control.h": "c"}, "C_Cpp_Runner.cCompilerPath": "gcc", "C_Cpp_Runner.cppCompilerPath": "g++", "C_Cpp_Runner.debuggerPath": "gdb", "C_Cpp_Runner.cStandard": "", "C_Cpp_Runner.cppStandard": "", "C_Cpp_Runner.msvcBatchPath": "C:/Program Files/Microsoft Visual Studio/VR_NR/Community/VC/Auxiliary/Build/vcvarsall.bat", "C_Cpp_Runner.useMsvc": false, "C_Cpp_Runner.warnings": ["-Wall", "-Wextra", "-Wpedantic", "-W<PERSON>dow", "-Wformat=2", "-Wcast-align", "-Wconversion", "-Wsign-conversion", "-W<PERSON><PERSON>-dereference"], "C_Cpp_Runner.msvcWarnings": ["/W4", "/permissive-", "/w14242", "/w14287", "/w14296", "/w14311", "/w14826", "/w44062", "/w44242", "/w14905", "/w14906", "/w14263", "/w44265", "/w14928"], "C_Cpp_Runner.enableWarnings": true, "C_Cpp_Runner.warningsAsError": false, "C_Cpp_Runner.compilerArgs": [], "C_Cpp_Runner.linkerArgs": [], "C_Cpp_Runner.includePaths": [], "C_Cpp_Runner.includeSearch": ["*", "**/*"], "C_Cpp_Runner.excludeSearch": ["**/build", "**/build/**", "**/.*", "**/.*/**", "**/.vscode", "**/.vscode/**"], "C_Cpp_Runner.useAddressSanitizer": false, "C_Cpp_Runner.useUndefinedSanitizer": false, "C_Cpp_Runner.useLeakSanitizer": false, "C_Cpp_Runner.showCompilationTime": false, "C_Cpp_Runner.useLinkTimeOptimization": false, "C_Cpp_Runner.msvcSecureNoWarnings": false, "C_Cpp.errorSquiggles": "disabled"}