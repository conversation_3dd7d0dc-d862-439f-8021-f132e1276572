#ifndef __ADC_KEY_H__
#define __ADC_KEY_H__

#include "./system/sys/sys.h"
#include "stdint.h"
#include "./system/event/system_event.h"
#include "./config/board_config.h"

typedef struct
{
    uint8_t adc_key_pin;
    uint16_t adc_value[TCFG_ADC_KEY_NUM];
    uint8_t key_value[TCFG_ADC_KEY_NUM];
} adc_key_platform_data_t;

void adc_key_init(void);
void process_adc_key_event(struct key_event *e);

#endif /* __ADC_KEY_H__ */
