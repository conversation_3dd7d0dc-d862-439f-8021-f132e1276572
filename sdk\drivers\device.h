
#ifndef _DEVICE_H_
#define _DEVICE_H_

// #include <kernel.h>

#include "string.h"

// #include <zephyr/types.h>


#define _INIT_LEVEL_DRIVER_CORE      0
#define _INIT_LEVEL_DRIVER          1
#define _INIT_LEVEL_MIDDWARE       2
#define _INIT_LEVEL_APPLICATION    3

#define _DO_CONCAT(x, y) x ## y
#define _CONCAT(x, y) _DO_CONCAT(x, y)

#define __used		__attribute__((__used__))

//level: DRIVER_CORE, DRIVER, MIDDWARE, APPLICATION

#define DEVICE_AND_API_INIT(dev_name, drv_name, init_fn, data, cfg_info, \
			    level, api) \
	static const struct device_config _CONCAT(__config_, dev_name) __used \
	__attribute__((__section__(".devconfig.init"))) = { \
		.name = drv_name, .init = (init_fn), \
		.config_info = (cfg_info) \
	}; \
	static const struct device _CONCAT(__device_, dev_name) __used \
	__attribute__((__section__(".device_"#level))) = { \
		 .config = &_CONCAT(__config_, dev_name), \
		 .driver_api = api, \
		 .driver_data = data \
	}


#define DEVICE_NAME_GET(name) (_CONCAT(__device_, name))

#define DEVICE_GET(name) (&DEVICE_NAME_GET(name))

#define DEVICE_DECLARE(name) static struct device DEVICE_NAME_GET(name)


#define _SYS_NAME(init_fn) _CONCAT(_CONCAT(sys_init_, init_fn), __COUNTER__)

#define SYS_INIT(init_fn, level) \
	DEVICE_AND_API_INIT(_SYS_NAME(init_fn), "", init_fn, NULL, NULL, level, NULL)


// struct device;

struct device {
	const struct device_config *config;
	const void *driver_api;
	void *driver_data;
};


struct device_config {
	char	*name;
	int (*init)(struct device *device);
	const void *config_info;
};


void _sys_device_do_config_level(int level);

struct device *device_get_binding(const char *name, unsigned char level);


#endif /* _DEVICE_H_ */
