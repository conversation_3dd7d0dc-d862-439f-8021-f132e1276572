<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Template</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pArmCC>5060960::V5.06 update 7 (build 960)::.\ARMCC</pArmCC>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::.\ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F407ZGTx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32F4xx_DFP.2.15.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IRAM2(0x10000000,0x00010000) IROM(0x08000000,0x00100000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0STM32F4xx_1024 -********** -********* -FP0($$Device:STM32F407ZGTx$CMSIS\Flash\STM32F4xx_1024.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:STM32F407ZGTx$Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32F407ZGTx$CMSIS\SVD\STM32F40x.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\Output\</OutputDirectory>
          <OutputName>project</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>..\Output\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>2</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>--no-multibyte-chars</MiscControls>
              <Define>USE_HAL_DRIVER,STM32F407xx</Define>
              <Undefine></Undefine>
              <IncludePath>..\Driver-Core\Driver-ST\Driver-F4\User;..\Driver-Core\Driver-ST\Driver-F4\CMSIS\Include;..\Driver-Core\Driver-ST\Driver-F4\CMSIS\Device\ST\STM32F4xx\Include;..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Inc;..\sdk;..\sdk\middleware\rtos\freertos\include;..\sdk\drivers\devices;..\sdk\drivers\devices\led\gpio_led;..\sdk\drivers;..\sdk\drivers\devices\motor</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>..\Output\project.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Startup</GroupName>
          <Files>
            <File>
              <FileName>startup_stm32f407xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\CMSIS\Device\ST\STM32F4xx\Source\Templates\startup_stm32f407xx.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/STM32F4xx_HAL_Driver</GroupName>
          <Files>
            <File>
              <FileName>stm32f4xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_dma_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_pwr_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_adc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_tim_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>User</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\User\main.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\User\stm32f4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>system_stm32f4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Driver-Core\Driver-ST\Driver-F4\User\system_stm32f4xx.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk/system</GroupName>
          <Files>
            <File>
              <FileName>sys.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\system\sys\sys.c</FilePath>
            </File>
            <File>
              <FileName>delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\system\delay\delay.c</FilePath>
            </File>
            <File>
              <FileName>debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\system\debug\debug.c</FilePath>
            </File>
            <File>
              <FileName>app_event.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\system\event\app_event.c</FilePath>
            </File>
            <File>
              <FileName>system_event.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\system\event\system_event.c</FilePath>
            </File>
            <File>
              <FileName>sdk_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\sdk_main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk/rtos/freertos</GroupName>
          <Files>
            <File>
              <FileName>croutine.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\middleware\rtos\freertos\src\croutine.c</FilePath>
            </File>
            <File>
              <FileName>event_groups.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\middleware\rtos\freertos\src\event_groups.c</FilePath>
            </File>
            <File>
              <FileName>list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\middleware\rtos\freertos\src\list.c</FilePath>
            </File>
            <File>
              <FileName>queue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\middleware\rtos\freertos\src\queue.c</FilePath>
            </File>
            <File>
              <FileName>stream_buffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\middleware\rtos\freertos\src\stream_buffer.c</FilePath>
            </File>
            <File>
              <FileName>tasks.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\middleware\rtos\freertos\src\tasks.c</FilePath>
            </File>
            <File>
              <FileName>timers.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\middleware\rtos\freertos\src\timers.c</FilePath>
            </File>
            <File>
              <FileName>heap_4.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\middleware\rtos\freertos\src\heap_4.c</FilePath>
            </File>
            <File>
              <FileName>port.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\middleware\rtos\freertos\src\port.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk/drivers</GroupName>
          <Files>
            <File>
              <FileName>key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\devices\input\key\key.c</FilePath>
            </File>
            <File>
              <FileName>gpio_key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\devices\input\key\gpio_key\gpio_key.c</FilePath>
            </File>
            <File>
              <FileName>adc_key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\devices\input\key\adc_key\adc_key.c</FilePath>
            </File>
            <File>
              <FileName>device.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\device.c</FilePath>
            </File>
            <File>
              <FileName>device.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\sdk\drivers\device.h</FilePath>
            </File>
            <File>
              <FileName>gpio_led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\devices\led\gpio_led\gpio_led.c</FilePath>
            </File>
            <File>
              <FileName>gpio_led.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\sdk\drivers\devices\led\gpio_led\gpio_led.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk/config</GroupName>
          <Files>
            <File>
              <FileName>board_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\config\board_config.c</FilePath>
            </File>
            <File>
              <FileName>board_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\sdk\config\board_config.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk/core</GroupName>
          <Files>
            <File>
              <FileName>driver_core.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\sdk\drivers\core\driver_core.h</FilePath>
            </File>
            <File>
              <FileName>adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\core\ST\F4\adc.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\core\ST\F4\gpio.c</FilePath>
            </File>
            <File>
              <FileName>i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\core\ST\F4\i2c.c</FilePath>
            </File>
            <File>
              <FileName>spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\core\ST\F4\spi.c</FilePath>
            </File>
            <File>
              <FileName>tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\core\ST\F4\tim.c</FilePath>
            </File>
            <File>
              <FileName>uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\core\ST\F4\uart.c</FilePath>
            </File>
            <File>
              <FileName>wdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\core\ST\F4\wdg.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk/middleware</GroupName>
          <Files>
            <File>
              <FileName>cJSON.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\middleware\cjson\cJSON.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk/devices</GroupName>
          <Files>
            <File>
              <FileName>driver_oled.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\devices\display\oled\driver_oled.c</FilePath>
            </File>
            <File>
              <FileName>driver_oled_data.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\sdk\drivers\devices\display\oled\driver_oled_data.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>atk_f407</LayName>
        <LayTarg>0</LayTarg>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
