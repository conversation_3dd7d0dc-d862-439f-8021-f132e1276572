#ifndef __DRIVER_CORE_H__
#define __DRIVER_CORE_H__

#include "./config/board_config.h"


#if TCFG_MCU_CHIP == TCFG_MCU_STM32F1


#elif TCFG_MCU_CHIP == TCFG_MCU_STM32F4

#include "./drivers/core/ST/F4/gpio.h"
#include "./drivers/core/ST/F4/uart.h"
#include "./drivers/core/ST/F4/adc.h"
#include "./drivers/core/ST/F4/wdg.h"
#include "./drivers/core/ST/F4/spi.h"
#include "./drivers/core/ST/F4/i2c.h"
#include "./drivers/core/ST/F4/tim.h"

#elif TCFG_MCU_CHIP == TCFG_MCU_TIMSP430

#else

#error "MCU not support"

#endif

#endif /* __DRIVER_CORE_H__ */
