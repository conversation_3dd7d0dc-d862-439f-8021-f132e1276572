#ifndef __LCD_H
#define __LCD_H

#include "stm32f1xx_hal.h"
#include "stdint.h"
#include "os_abstract.h"

#define LCD_ST7789 1
#define LCD_ILI9341 2
#define LCD_ILI9486 3

#define LCD_TYPE  LCD_ST7789

#define LCD_CS_PORT GPIOA
#define LCD_CS_PIN GPIO_PIN_6

#define LCD_DC_PORT GPIOC
#define LCD_DC_PIN GPIO_PIN_4

#define LCD_RST_PORT GPIOC
#define LCD_RST_PIN GPIO_PIN_5

#define LCD_BL_PORT GPIOA
#define LCD_BL_PIN GPIO_PIN_4

#define LCD_SPI_HANDLE hspi1
#define LCD_SPI_DMA_EN  1

#define LCD_WIDTH 240
#define LCD_HEIGHT 320

#define LCD_BUFFER_WIDTH 240
#define LCD_BUFFER_HEIGHT 20

#define LCD_BUFFER_SIZE (LCD_BUFFER_WIDTH * LCD_BUFFER_HEIGHT * 2)
#define LCD_SHOW_SIZE (LCD_WIDTH * LCD_HEIGHT * 2)

extern uint8_t lcd_buffer[LCD_BUFFER_WIDTH * LCD_BUFFER_HEIGHT * 2];
extern const unsigned char gImage_hua[];

//#define lcd_delay(x) HAL_Delay(x)
#define lcd_delay(x) os_task_delay(x)

#define lcd_cs(x) HAL_GPIO_WritePin(LCD_CS_PORT, LCD_CS_PIN, (GPIO_PinState)x)
#define lcd_dc(x) HAL_GPIO_WritePin(LCD_DC_PORT, LCD_DC_PIN, (GPIO_PinState)x)
#define lcd_rst(x) HAL_GPIO_WritePin(LCD_RST_PORT, LCD_RST_PIN, (GPIO_PinState)x)
#define lcd_bl(x) HAL_GPIO_WritePin(LCD_BL_PORT, LCD_BL_PIN, (GPIO_PinState)x)

void lcd_init(void);
void lcd_gpio_init(void);
void lcd_send_byte(uint8_t *data, uint16_t len);
void lcd_send_cmd(uint8_t cmd);
void lcd_send_data(uint8_t data);
void lcd_show_image(void);
void lcd_show_colors_test(void);
void lcd_show_image_test(void);

#endif
