#include "./middleware/rtos/os_abstract.h"
#include "./system/event/system_event.h"
#include "./drivers/devices/input/key/key.h"
#include "./system/sys/sys.h"

static os_queue_handle_t sys_event_queue = NULL;
static os_task_handle_t sys_event_task = NULL;
static void process_sys_event(void *arg);

void sys_event_init(void)
{
    sys_event_queue = os_queue_create(5, sizeof(struct sys_event)); // 创建一个队列，用于存放系统事件
    os_task_create(process_sys_event, "sys_event_task", 128 * 1, NULL, 3, &sys_event_task);
}

void sys_event_send_notify(struct sys_event *e)
{
    os_queue_send(sys_event_queue, e, 0); // 将系统事件放入队列中
}

void sys_event_get_notify(struct sys_event *e)
{
    os_queue_receive(sys_event_queue, e, os_wait_max); // 从队列中取出系统事件
}

// 示例：事件处理函数
static void process_sys_event(void *arg)
{

    while (1)
    {
        struct sys_event e;
        sys_event_get_notify(&e);
        switch (e.type)
        {
        case SYS_KEY_EVENT:
            process_key_event(&e.u.key);
            break;
        }
        os_task_delay(100);
    }
}
