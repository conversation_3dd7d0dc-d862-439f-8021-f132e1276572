#include "Motor.h"
#include "drivers/core/ST/F4/gpio.h"
#include "drivers/core/ST/F4/tim.h"

extern TIM_HandleTypeDef tim1_pwm_handle;
extern TIM_HandleTypeDef tim2_pwm_handle;
extern TIM_HandleTypeDef tim3_pwm_handle;

struct gpio_motor_config
{
	uint8_t AIN1;
	uint8_t AIN2;
	uint8_t BIN1;
	uint8_t BIN2;
};
struct gpio_motor_api
{
	void (*led_red)(struct device *dev);
	void (*led_green)(struct device *dev);
	void (*led_blue)(struct device *dev);
	void (*led_off)(struct device *dev);
};

struct gpio_motor_config _gpio_motor_config =
	{
		.AIN1 = PD11,
		.AIN2 = PD10,
		.BIN1 = PD9,
		.BIN2 = PD8,
};

// 左右电机、编码器、PID初始化
int Motor_All_Init(struct device *dev)
{
	struct gpio_motor_config *config = (struct gpio_motor_config *)dev->config->config_info;
	gpio_pin_init(config->AIN1, OUT_PP, PULLDOWN, LOW);
	gpio_pin_init(config->AIN2, OUT_PP, PULLUP, LOW);
	gpio_pin_init(config->BIN1, OUT_PP, PULLDOWN, LOW);
	gpio_pin_init(config->BIN2, OUT_PP, PULLUP, LOW);

	tim_pwm_pin_init(4, TIM_CHANNEL_2, 1000 - 1, 840 - 1); // PWM引脚初始化
	// HAL_TIM_PWM_Start(&htim4, TIM_CHANNEL_2);			   // 开启PWM
	tim_pwm_pin_init(4, TIM_CHANNEL_1, 1000 - 1, 840 - 1); // PWM引脚初始化
	// HAL_TIM_PWM_Start(&htim4, TIM_CHANNEL_1);			   // 开启PWM
	HAL_TIM_Encoder_Start(&tim3_pwm_handle, TIM_CHANNEL_1); // 开启编码器A
	HAL_TIM_Encoder_Start(&tim3_pwm_handle, TIM_CHANNEL_2); // 开启编码器A
	HAL_TIM_Encoder_Start(&tim1_pwm_handle, TIM_CHANNEL_1); // 开启编码器B
	HAL_TIM_Encoder_Start(&tim1_pwm_handle, TIM_CHANNEL_2); // 开启编码器B
	HAL_TIM_Base_Start_IT(&tim2_pwm_handle);				// 使能定时器2中断
	PID_param_init();
	return 0;
}

///****************右边电机引脚初始化**************/
///* 设置速度（占空比） */
// void SET_R_FWD_COMPAER(ChannelPulse)
//{
//	Right_moto_go();          //右电机往前
//	Right_PWM_SetCompare(ChannelPulse);
// }

// void SET_R_REV_COMPAER(ChannelPulse)
//{
//	Right_moto_back();     		//右电机往后
//	Right_PWM_SetCompare(ChannelPulse);
// }

///****************左边电机引脚初始化**************/      //当两个轮子转向不一致的时候，要同时调这里的宏定义和encode.c编码器捕获脉冲的正负
///* 设置速度（占空比）2 */
// void SET_L_FWD_COMPAER(ChannelPulse)
//{
//	Left_moto_go();
//	Left_PWM_SetCompare(ChannelPulse);
// }

// void SET_L_REV_COMPAER(ChannelPulse)
//{
//	Left_moto_back();
//	Left_PWM_SetCompare(ChannelPulse);
// }

///* 禁用输出 */
// void MOTORDISABLE()
//{
//	TIM_Cmd(TIM4, DISABLE);
//	Right_moto_Stop();     		//右电机停止
//	Left_moto_Stop();
// }
void Right_PWM_SetCompare(uint16_t Compare) // 这个是设置ccr的，占空比=ccr/（arr+1），所以这个ccr的值为0-100
{											// 这里是1ms来一个周期，所以1s里面有1000个脉冲     tim4   PB6
	// __HAL_TIM_SET_COMPARE(&htim4, TIM_CHANNEL_1, Compare);
	tim_pwm_set_ccr(4, TIM_CHANNEL_1, Compare);
}

void Left_PWM_SetCompare(uint16_t Compare)
{
	// __HAL_TIM_SET_COMPARE(&htim4, TIM_CHANNEL_2, Compare);
	tim_pwm_set_ccr(4, TIM_CHANNEL_2, Compare);
}
// 小车整体控制
void car_move()
{
	if (RightSpeed_Out >= 0 && LeftSpeed_Out >= 0) // 前进
		run(RightSpeed_Out, LeftSpeed_Out);

	if (RightSpeed_Out < 0 && LeftSpeed_Out < 0) // 后退
		backrun(RightSpeed_Out, LeftSpeed_Out);

	if (RightSpeed_Out > 0 && LeftSpeed_Out < 0) // 左转
		leftturn(RightSpeed_Out, LeftSpeed_Out);

	if (RightSpeed_Out < 0 && LeftSpeed_Out > 0) // 右转
		rightturn(RightSpeed_Out, LeftSpeed_Out);
}

// 小车调速函数
void Speed_Control(uint16_t Right, uint16_t Left) // 这个到时候传入pid的值 传入speed值
{
	Right_PWM_SetCompare(Right); // 调速		//改变比较值，改变占空比，这个值为0-100
	Left_PWM_SetCompare(Left);
}

// 小车前进函数
void run(uint16_t Right, uint16_t Left)
{
	Speed_Control(Right, Left);
	Right_moto_go(); // 右电机往前
	Left_moto_go();
}

// 小车后退函数
void backrun(uint16_t Right, uint16_t Left)
{
	Speed_Control(Right, Left);
	Right_moto_back(); // 右电机往后
	Left_moto_back();
}

// 小车左转函数
void leftturn(uint16_t Right, uint16_t Left)
{
	Speed_Control(Right, Left);
	Right_moto_go();  // 右电机往前
	Left_moto_back(); // 左电机往后
}

// 小车右转函数
void rightturn(uint16_t Right, uint16_t Left)
{
	Speed_Control(Right, Left);
	Right_moto_back(); // 右电机往后
	Left_moto_go();	   // 左电机往前
}

// 小车停车函数
void stop(void)
{
	Speed_Control(0, 0);
	Right_moto_Stop(); // 右电机停止
	Left_moto_Stop();
}

// 右
// 电机向前
void Right_moto_go(struct device *dev)
{
	// 正转
	struct gpio_motor_config *config = (struct gpio_motor_config *)dev->config->config_info;
	gpio_write_pin(config->BIN1, HIGH);
	gpio_write_pin(config->BIN2, LOW);
	// HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_SET);
	// HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_RESET);
}

// 电机向后
void Right_moto_back(struct device *dev)
{
	// 反转
	struct gpio_motor_config *config = (struct gpio_motor_config *)dev->config->config_info;
	gpio_write_pin(config->BIN1, LOW);
	gpio_write_pin(config->BIN2, HIGH);
	// HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_RESET);
	// HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_SET);
}

// 电机停止
void Right_moto_Stop(struct device *dev)
{
	// 停车
	struct gpio_motor_config *config = (struct gpio_motor_config *)dev->config->config_info;
	gpio_write_pin(config->BIN1, LOW);
	gpio_write_pin(config->BIN2, LOW);
	// HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_RESET);
	// HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_RESET);
}

// 左
// 电机向前
void Left_moto_go(struct device *dev)
{
	// 正转
	struct gpio_motor_config *config = (struct gpio_motor_config *)dev->config->config_info;
	gpio_write_pin(config->AIN1, HIGH);
	gpio_write_pin(config->AIN2, LOW);
	// HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_SET);
	// HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_RESET);
}

// 电机向后
void Left_moto_back(struct device *dev)
{
	// 反转
	struct gpio_motor_config *config = (struct gpio_motor_config *)dev->config->config_info;
	gpio_write_pin(config->AIN1, LOW);
	gpio_write_pin(config->AIN2, HIGH);
	// HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_RESET);
	// HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_SET);
}

// 电机停止
void Left_moto_Stop(struct device *dev)
{
	// 停车
	struct gpio_motor_config *config = (struct gpio_motor_config *)dev->config->config_info;
	gpio_write_pin(config->AIN1, LOW);
	gpio_write_pin(config->AIN2, LOW);
	// HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_RESET);
	// HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_RESET);
}

struct gpio_motor_api _gpio_motor_api =
	{
		.led_red = gpio_led_red,
		.led_green = gpio_led_green,
		.led_blue = gpio_led_blue,
		.led_off = gpio_led_off,
};