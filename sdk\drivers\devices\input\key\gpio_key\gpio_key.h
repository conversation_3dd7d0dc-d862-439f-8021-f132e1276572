#ifndef __IO_KEY_H
#define __IO_KEY_H

#include "./system/sys/sys.h"
#include "stdint.h"
#include "./system/event/system_event.h"
#include "./config/board_config.h"
typedef struct
{
    uint8_t pin[TCFG_IO_KEY_USER_MAX];
    uint8_t key_value[TCFG_IO_KEY_USER_MAX];
    uint8_t click_status[TCFG_IO_KEY_USER_MAX];
} gpio_key_platform_data_t;

void gpio_key_init(void);
void process_gpio_key_event(struct key_event *e);
#endif
