#ifndef __UART_H
#define __UART_H

#include "stdint.h"

/**
 * @brief       初始化串口
 * @param       uart_id         串口号
 * @param       baudrate        波特率
 * @param       tx_pin          发送引脚号
 * @param       rx_pin          接收引脚号
 * @param       mode            引脚模式: 1:开启中断接收 0:不开启中断接收
 * @param       priority        中断优先级
 * @note        无
 * @retval      无
 */
void uart_init(uint8_t uart_id, uint32_t baudrate, uint8_t tx_pin, uint8_t rx_pin, uint8_t mode, uint8_t priority);

/**
 * @brief       发送数据
 * @param       uart_id       串口号
 * @param       data       发送数据
 * @param       len       发送数据长度
 * @param       timeout       超时时间
 * @note        无
 * @retval      无
 */
void uart_send_data(uint8_t uart_id, uint8_t *data, uint16_t len, uint32_t timeout);

/**
 * @brief       发送数据
 * @param       uart_id       串口号
 * @param       callback       接收回调函数
 * @note        无
 * @retval      无
 */
void uart_add_recv_callback(uint8_t uart_id, void (*callback)(uint8_t));

#endif /* __UART_H */
