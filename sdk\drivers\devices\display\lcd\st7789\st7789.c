#include "devices/display/lcd/driver_lcd.h"
#include "devices/display/lcd/st7789/st7789.h"

#if LCD_TYPE == LCD_ST7789

void lcd_st7789_init(void)
{
    lcd_gpio_init();
    lcd_rst(0);
    lcd_delay(120);
    lcd_rst(1);
    lcd_delay(120);
    lcd_send_cmd(0x11);
    lcd_delay(120);
    lcd_send_cmd(0x36);
    lcd_send_data(0x00);

    lcd_send_cmd(0x3A);
    lcd_send_data(0x55);

    lcd_send_cmd(0xB2);
    lcd_send_data(0x0C);
    lcd_send_data(0x0C);
    lcd_send_data(0x00);
    lcd_send_data(0x33);
    lcd_send_data(0x33);

    lcd_send_cmd(0xB7);
    lcd_send_data(0x72);

    lcd_send_cmd(0xBB);
    lcd_send_data(0x3D);

    lcd_send_cmd(0xC0);
    lcd_send_data(0x2C);

    lcd_send_cmd(0xC2);
    lcd_send_data(0x01);

    lcd_send_cmd(0xC3);
    lcd_send_data(0x19);

    lcd_send_cmd(0xC4);
    lcd_send_data(0x20);

    lcd_send_cmd(0xC6);
    lcd_send_data(0x0F);

    lcd_send_cmd(0xD0);
    lcd_send_data(0xA4);
    lcd_send_data(0xA1);

    lcd_send_cmd(0xE0);
    lcd_send_data(0xD0);
    lcd_send_data(0x04);
    lcd_send_data(0x0D);
    lcd_send_data(0x11);
    lcd_send_data(0x13);
    lcd_send_data(0x2B);
    lcd_send_data(0x3F);
    lcd_send_data(0x54);
    lcd_send_data(0x4C);
    lcd_send_data(0x18);
    lcd_send_data(0x0D);
    lcd_send_data(0x0B);
    lcd_send_data(0x1F);
    lcd_send_data(0x23);

    lcd_send_cmd(0xE1);
    lcd_send_data(0xD0);
    lcd_send_data(0x04);
    lcd_send_data(0x0C);
    lcd_send_data(0x11);
    lcd_send_data(0x13);
    lcd_send_data(0x2C);
    lcd_send_data(0x3F);
    lcd_send_data(0x44);
    lcd_send_data(0x51);
    lcd_send_data(0x2F);
    lcd_send_data(0x1F);
    lcd_send_data(0x1F);
    lcd_send_data(0x20);
    lcd_send_data(0x23);

    lcd_send_cmd(0x20);

    lcd_send_cmd(0x29);

    lcd_st7789_clear_color(0x0000);
}

void lcd_st7789_set_window(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2)
{
    if (x1 > (LCD_WIDTH - 1) || x2 > (LCD_WIDTH - 1) || x2 < x1)
        return;
    if (y1 > (LCD_HEIGHT - 1) || y2 > (LCD_HEIGHT - 1) || y2 < y1)
        return;

    lcd_send_cmd(0x2A);
    lcd_send_data(x1 >> 8);
    lcd_send_data(x1);
    lcd_send_data(x2 >> 8);
    lcd_send_data(x2);

    lcd_send_cmd(0x2B);
    lcd_send_data(y1 >> 8);
    lcd_send_data(y1);
    lcd_send_data(y2 >> 8);
    lcd_send_data(y2);
    lcd_send_cmd(0x2C);
}

void lcd_st7789_clear_color(uint16_t color)
{
    uint32_t i;
    lcd_st7789_set_window(0, 0, LCD_BUFFER_WIDTH - 1, LCD_HEIGHT - 1);
    for (i = 0; i < LCD_BUFFER_WIDTH * LCD_BUFFER_HEIGHT; i++)
    {
        lcd_buffer[i * 2] = color >> 8;
        lcd_buffer[i * 2 + 1] = color & 0xFF;
    }
    lcd_dc(1);
    lcd_cs(0);
    for (i = 0; i < LCD_HEIGHT / LCD_BUFFER_HEIGHT; i++)
        lcd_send_byte(lcd_buffer, LCD_BUFFER_WIDTH * LCD_BUFFER_HEIGHT * 2);
    lcd_cs(1);
}

void lcd_st7789_show_image(void)
{
    lcd_st7789_set_window(0, 0, LCD_WIDTH - 1, LCD_HEIGHT - 1);
    for (uint32_t i = 0; i < LCD_HEIGHT / LCD_BUFFER_HEIGHT; i++)
    {
        uint32_t count = i * LCD_BUFFER_SIZE;
        for (uint32_t j = 0; j < LCD_BUFFER_SIZE; j++)
        {
            lcd_buffer[j] = gImage_hua[j + count];
        }
        lcd_dc(1);
        lcd_cs(0);
        lcd_send_byte(lcd_buffer, LCD_BUFFER_SIZE);
        lcd_cs(1);
    }
}

void lcd_st7789_show_scroll_image(void)
{
    static uint32_t scroll_offset = 0;

    // 刷新窗口上方的内容
    if (scroll_offset > 0)
    {
        uint32_t top_offset = LCD_SHOW_SIZE - scroll_offset * LCD_WIDTH * 2;

        lcd_st7789_set_window(0, 0, LCD_WIDTH - 1, scroll_offset - 1);

        for (uint32_t i = 0; i < scroll_offset / LCD_BUFFER_HEIGHT; i++)
        {
            for (uint32_t j = 0; j < LCD_BUFFER_SIZE; j++)
            {
                lcd_buffer[j] = gImage_hua[top_offset + j + i * LCD_BUFFER_SIZE];
            }
            lcd_dc(1);
            lcd_cs(0);
            lcd_send_byte(lcd_buffer, LCD_BUFFER_SIZE);
            lcd_cs(1);
        }
    }

    lcd_st7789_set_window(0, scroll_offset, LCD_WIDTH - 1, LCD_HEIGHT - 1);

    for (uint32_t i = 0; i < (LCD_HEIGHT - scroll_offset) / LCD_BUFFER_HEIGHT; i++)
    {
        for (uint32_t j = 0; j < LCD_BUFFER_SIZE; j++)
        {
            lcd_buffer[j] = gImage_hua[j + i * LCD_BUFFER_SIZE];
        }
        lcd_dc(1);
        lcd_cs(0);
        lcd_send_byte(lcd_buffer, LCD_BUFFER_SIZE);
        lcd_cs(1);
    }

    // 更新滚动偏移量
    scroll_offset += LCD_BUFFER_HEIGHT;
    if (scroll_offset >= LCD_HEIGHT)
    {
        scroll_offset = 0;
    }
}

#endif
