#ifndef _SYS_H
#define _SYS_H

#include "stm32f4xx.h"
#include "core_cm4.h"
#include "stm32f4xx_hal.h"

void sys_standby(void);                                                                   /* 进入待机模式 */
void sys_soft_reset(void);                                                                /* 系统软复位 */
uint8_t sys_stm32_clock_init(uint32_t plln, uint32_t pllm, uint32_t pllp, uint32_t pllq); /* 配置系统时钟 */
void sys_check_rst(void);
uint32_t sys_get_tick(void);

#endif
