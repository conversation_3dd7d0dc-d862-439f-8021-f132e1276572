#ifndef __KEY_H
#define __KEY_H

#include "stdint.h"
#include "./system/event/system_event.h"
enum
{
    KEY_EVENT_CLICK,
    KEY_EVENT_LONG,
    KEY_EVENT_HOLD,
    KEY_EVENT_UP,
    KEY_EVENT_DOUBLE_CLICK,
    KEY_EVENT_TRIPLE_CLICK,
    KEY_EVENT_FOURTH_CLICK,
    KEY_EVENT_FIRTH_CLICK,
    KEY_EVENT_USER,
    KEY_EVENT_MAX,
};

typedef enum __KEY_DRIVER_TYPE
{
    KEY_DRIVER_TYPE_IO = 0x0,
    KEY_DRIVER_TYPE_ADC,
    KEY_DRIVER_TYPE_RTCVDD_AD,
    KEY_DRIVER_TYPE_IR,
    KEY_DRIVER_TYPE_TOUCH,
    KEY_DRIVER_TYPE_CTMU_TOUCH,
    KEY_DRIVER_TYPE_RDEC,
    KEY_DRIVER_TYPE_SLIDEKEY,
    K<PERSON><PERSON>_DRIVER_TYPE_SOFTKEY,
    KEY_DRIVER_TYPE_BRIGHTNESS,
    <PERSON>EY_DRIVER_TYPE_VOICE,

    KEY_DRIVER_TYPE_MAX,
} KEY_DRIVER_TYPE;

typedef struct
{
    const uint32_t scan_time; // 按键扫描频率, 单位ms
    uint8_t last_key;         // 上一次get_value按键值
    //== 用于消抖类参数
    uint8_t filter_value;      // 用于按键消抖
    uint8_t filter_cnt;        // 用于按键消抖时的累加值
    const uint8_t filter_time; // 当filter_cnt累加到base_cnt值时, 消抖有效
    //== 用于判定长按和HOLD事件参数
    const uint8_t long_time; // 按键判定长按数量
    const uint8_t hold_time; // 按键判定HOLD数量
    uint8_t press_cnt;       // 与long_time和hold_time对比, 判断long_event和hold_event
    //== 用于判定连击事件参数
    uint8_t click_cnt;              // 单击次数
    uint8_t click_delay_cnt;        // 按键被抬起后等待连击事件延时计数
    const uint8_t click_delay_time; ////按键被抬起后等待连击事件延时数量
    uint8_t notify_value;           // 在延时的待发送按键值
    uint8_t key_type;
    uint8_t (*get_value)(void);
} key_driver_para_t;

void key_init(void);
void process_key_event(struct key_event *e);

#endif
