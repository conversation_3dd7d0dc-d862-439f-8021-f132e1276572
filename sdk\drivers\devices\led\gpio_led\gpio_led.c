#include "gpio_led.h"
#include "drivers/core/ST/F4/gpio.h"
#include "stdint.h"
#include "./middleware/rtos/os_abstract.h"

#define CONFIG_GPIO_LED_NAME "gpio_led"


struct gpio_led_config
{
    uint8_t GPIO_R;
    uint8_t GPIO_G;
    uint8_t GPIO_B;
		uint8_t GPIO_LED1;
};

struct gpio_led_api
{
    void (*led_red)(struct device *dev);
    void (*led_green)(struct device *dev);
    void (*led_blue)(struct device *dev);
    void (*led_off)(struct device *dev);
		void (*led1_on)(struct device *dev);
};


struct gpio_led_config _gpio_led_config = 
{
    .GPIO_R = PA0,
    .GPIO_G = PA1,
    .GPIO_B = PA2,
		.GPIO_LED1 = PB12,
};

void gpio_led1_on(struct device *dev)
{
    struct gpio_led_config *config = (struct gpio_led_config *)dev->config->config_info;
    gpio_write_pin(config->GPIO_LED1, LOW);
}

void gpio_led_red(struct device *dev)
{
    struct gpio_led_config *config = (struct gpio_led_config *)dev->config->config_info;
    gpio_write_pin(config->GPIO_R, HIGH);
}

void gpio_led_green(struct device *dev)
{
    struct gpio_led_config *config = (struct gpio_led_config *)dev->config->config_info;
    gpio_write_pin(config->GPIO_G, HIGH);
}

void gpio_led_blue(struct device *dev)
{
    struct gpio_led_config *config = (struct gpio_led_config *)dev->config->config_info;
    gpio_write_pin(config->GPIO_B, HIGH);
}

void gpio_led_off(struct device *dev)
{
    struct gpio_led_config *config = (struct gpio_led_config *)dev->config->config_info;
    gpio_write_pin(config->GPIO_R, LOW);
    gpio_write_pin(config->GPIO_G, LOW);
    gpio_write_pin(config->GPIO_B, LOW);
}

struct gpio_led_api _gpio_led_api = 
{
    .led_red = gpio_led_red,
    .led_green = gpio_led_green,
    .led_blue = gpio_led_blue,
    .led_off = gpio_led_off,
		//.led1_on = gpio_led1_on,
};



void gpio_led_run(struct device *dev)
{

    struct gpio_led_config *config = (struct gpio_led_config *)dev->config->config_info;
//    gpio_pin_init(config->GPIO_R, OUT_PP, NOPULL, LOW);
//    gpio_pin_init(config->GPIO_G, OUT_PP, NOPULL, LOW);
//    gpio_pin_init(config->GPIO_B, OUT_PP, NOPULL, LOW);
	  gpio_pin_init(config->GPIO_LED1, OUT_PP, PULLUP, LOW);
		gpio_write_pin(config->GPIO_LED1, HIGH);
}

int gpio_led_init(void *arg)
{
	//os_task_create(gpio_led_run, "gpio_led_run", 32, NULL, 0, NULL);
	return 0;
}

DEVICE_AND_API_INIT(gpio_led, CONFIG_GPIO_LED_NAME, gpio_led_run, NULL, &_gpio_led_config, DRIVER, &_gpio_led_api);
