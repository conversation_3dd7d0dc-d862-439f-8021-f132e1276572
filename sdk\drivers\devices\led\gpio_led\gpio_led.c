#include "gpio_led.h"
#include "drivers/core/ST/F4/gpio.h"
#include "stdint.h"

#define CONFIG_GPIO_LED_NAME "gpio_led"


struct gpio_led_config
{
    uint8_t GPIO_R;
    uint8_t GPIO_G;
    uint8_t GPIO_B;
};

struct gpio_led_api
{
    void (*led_red)(struct device *dev);
    void (*led_green)(struct device *dev);
    void (*led_blue)(struct device *dev);
    void (*led_off)(struct device *dev);
};


struct gpio_led_config _gpio_led_config = 
{
    .GPIO_R = PA0,
    .GPIO_G = PA1,
    .GPIO_B = PA2,
};


int gpio_led_init(struct device *dev)
{

    struct gpio_led_config *config = (struct gpio_led_config *)dev->config->config_info;
    gpio_pin_init(config->GPIO_R, OUT_PP, NOPULL, LOW);
    gpio_pin_init(config->GPIO_G, OUT_PP, NOPULL, LOW);
    gpio_pin_init(config->GPIO_B, OUT_PP, NOPULL, LOW);

    return 0;
}

void gpio_led_red(struct device *dev)
{
    struct gpio_led_config *config = (struct gpio_led_config *)dev->config->config_info;
    gpio_write_pin(config->GPIO_R, HIGH);
}

void gpio_led_green(struct device *dev)
{
    struct gpio_led_config *config = (struct gpio_led_config *)dev->config->config_info;
    gpio_write_pin(config->GPIO_G, HIGH);
}

void gpio_led_blue(struct device *dev)
{
    struct gpio_led_config *config = (struct gpio_led_config *)dev->config->config_info;
    gpio_write_pin(config->GPIO_B, HIGH);
}

void gpio_led_off(struct device *dev)
{
    struct gpio_led_config *config = (struct gpio_led_config *)dev->config->config_info;
    gpio_write_pin(config->GPIO_R, LOW);
    gpio_write_pin(config->GPIO_G, LOW);
    gpio_write_pin(config->GPIO_B, LOW);
}

struct gpio_led_api _gpio_led_api = 
{
    .led_red = gpio_led_red,
    .led_green = gpio_led_green,
    .led_blue = gpio_led_blue,
    .led_off = gpio_led_off,
};


DEVICE_AND_API_INIT(gpio_led, CONFIG_GPIO_LED_NAME, gpio_led_init, NULL, &_gpio_led_config, DRIVER, &_gpio_led_api);
