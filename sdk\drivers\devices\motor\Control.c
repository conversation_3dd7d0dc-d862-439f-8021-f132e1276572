#include "Control.h"

float Right_current_Speed, Left_current_Speed;
float Right_current_cm, Left_current_cm;

float g_fTargetJourney = 0; // 存放小车左右轮所走路程和 ， 单位cm，需要在下一阶段任务中设置

float LeftSpeed_Out, LeftLocation_Out;
float RightSpeed_Out, RightLocation_Out;

void car_go(int32_t target_location_cm)
{
	float Car_location;

	g_fTargetJourney = target_location_cm; // 防止长时间PID控制用

	Car_location = target_location_cm * (Reduction_ratio * Frequency_Divide) / (2 * WheelR * 3.1416);
	// printf("Car_location = %f\n", Car_location);
	set_pid_target(&g_tRightLocalPID, Car_location);
	set_pid_target(&g_tLeftLocalPID, Car_location);

	//	Location_Speed_control();

	g_lMotor_Lef_PulseSigma = 0; // 这个一定要清0，不然会一直累加
	g_lMotor_Rig_PulseSigma = 0;
}

void spin_turn(spin_dir_t zhuanxiang)
{
	float spin90_val; // 小车转完所走过的路程。
	float Car_Turn_val;

	spin90_val = 0.25 * 3.1416 * LUN_JU; // 转90度，即圆的1/4,即（0.25*3.1416），LUN_JU则是轮子到车中间的距离

	/****转弯后不循线可以调转向系数**********/
	if (zhuanxiang == left_90) // openmv识别到需要往左边病房走
	{
		Car_Turn_val = spin90_val;			//( spin90_val / (4.8 * 3.142) ) *   (500*4*20) ;
		Car_Turn_val = 0.95f * Car_Turn_val; // 90*0.94 = 84.6   //惯性影响，导致转弯比理论设定的多。直接设90度接下来的巡线就寻不回来了
	}
	else if (zhuanxiang == right_90) // openmv识别到需要往右边病房走
	{
		Car_Turn_val = -(spin90_val / (4.8 * 3.142)) * (500 * 4 * 20);
		Car_Turn_val = 0.98f * Car_Turn_val; // 90*0.96 = 86.4    //惯性影响，导致转弯比理论设定的多。 接下来的巡线可能就寻不回来了
	}
	else if (zhuanxiang == back_180)
	{
		Car_Turn_val = -(spin90_val / (4.8 * 3.142)) * (500 * 4 * 20);
		Car_Turn_val = 0.98f * Car_Turn_val; // 同理
		Car_Turn_val = 2 * Car_Turn_val;	// 0.96*180 = 175.5
	}

	set_pid_target(&g_tRightLocalPID, -Car_Turn_val); // 转向，一个往前一个往后，实现原地差速转弯
	set_pid_target(&g_tLeftLocalPID, Car_Turn_val);

	g_lMotor_Lef_PulseSigma = 0; // 这个一定要清0，不然会一直累加
	g_lMotor_Rig_PulseSigma = 0;
}

void Location_Speed_control(void) // 原地转向可以直接 调用这个
{
	// mpu_dmp_get_data放在这会影响到编码器脉冲的获取。
	//	u32 capture_value = Encoder_Get();  //不能在两个两次调用Encoder_Get()这个函数，不然第二个值是接收不到的
	//	Right_current_cm = g_lMotor_Rig_PulseSigma * (2 * WheelR * 3.1416) / (Reduction_ratio * Frequency_Divide );   //  cm
	//	current_Speed = capture_value * 100 / (Reduction_ratio * Frequency_Divide );   //  r/s

	RightLocation_Out = Right_location_pid_control(); //
	LeftLocation_Out = Left_location_pid_control();

	set_pid_target(&g_tRightSpeedPID, RightLocation_Out); // 每次都必须有位置环的值      设置目标速度  将到目标位置还需要捕获多少个脉冲数设置为速度的target值
	set_pid_target(&g_tLeftSpeedPID, LeftLocation_Out);	  // 每次都必须有位置环的值   这个是右边电机的

	RightSpeed_Out = Right_speed_pid_control(); // 要是电机转向不符合预期，就在这两句里取反数值
	LeftSpeed_Out = Left_speed_pid_control();	// 这里加循迹补偿
}

float Right_location_pid_control(void)
{
	float cont_val = 0.0;
	int32_t actual_location;

	actual_location = Right_current_cm * (Reduction_ratio * Frequency_Divide) / (2 * WheelR * 3.1416); // 1圈 = 2464个脉冲 = 56*11*4  //这里位置用圈数代替。

	cont_val = location_pid_realize(&g_tRightLocalPID, actual_location);

	// 还没加串级pID之前，位置环的cont_val对应PWM。 改成串级PID后，位置换的cont_val对应目标速度

	/* 目标速度上限处理 */
	if (cont_val > TARGET_SPEED_MAX)
	{
		cont_val = TARGET_SPEED_MAX;
	}
	else if (cont_val < -TARGET_SPEED_MAX)
	{
		cont_val = -TARGET_SPEED_MAX;
	}

	return cont_val;
}

float Right_speed_pid_control(void)
{

	float cont_val = 0.0; // 当前控制值
	int32_t actual_speed;

	// 转速 r/m每分钟多少转            *1000是从ms->s,*60是从s->minute      * 50 / (Reduction_ratio * Frequency_Divide * SPEED_PID_PERIOD)
	actual_speed = g_nMotor_Rig_Pulse; //((float)g_nMotor_Rig_Pulse*1000.0*60)/(Reduction_ratio * Frequency_Divide * SPEED_PID_PERIOD);

	cont_val = speed_pid_realize(&g_tRightSpeedPID, actual_speed); // 进行 PID 计算

	//	/* 目标速度上限处理 */
	//	if (cont_val > TARGET_SPEED_MAX)
	//	{
	//		cont_val = TARGET_SPEED_MAX;
	//	}
	//	else if (cont_val < -TARGET_SPEED_MAX)
	//	{
	//		cont_val = -TARGET_SPEED_MAX;
	//	}

	return cont_val;
}

float Left_location_pid_control(void)
{
	float cont_val = 0.0;
	int32_t actual_location;

	actual_location = Left_current_cm * (Reduction_ratio * Frequency_Divide) / (2 * WheelR * 3.1416); // 1圈 = 2464个脉冲 = 56*11*4  //这里位置用圈数代替。

	cont_val = location_pid_realize(&g_tLeftLocalPID, actual_location);

	// 改成串级PID后，位置换的cont_val对应目标速度

	// 目标速度限幅
	/* 目标速度上限处理 */
	if (cont_val > TARGET_SPEED_MAX)
	{
		cont_val = TARGET_SPEED_MAX;
	}
	else if (cont_val < -TARGET_SPEED_MAX)
	{
		cont_val = -TARGET_SPEED_MAX;
	}

	return cont_val;
}

float Left_speed_pid_control(void)
{

	float cont_val = 0.0; // 当前控制值
	int32_t actual_speed;

	actual_speed = g_nMotor_Lef_Pulse;
	//	  actual_speed = ((float)g_nMotor_Lef_Pulse*1000.0*60.0)/(Reduction_ratio * Frequency_Divide * SPEED_PID_PERIOD);

	cont_val = speed_pid_realize(&g_tLeftSpeedPID, actual_speed); // 进行 PID 计算

	//
	return cont_val;
}
