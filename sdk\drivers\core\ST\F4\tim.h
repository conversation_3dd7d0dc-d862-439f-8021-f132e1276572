#ifndef __TIM_H__
#define __TIM_H__

/**
 * @brief        TIM PWM引脚初始化
 * @param       tim_id       TIM ID号
 * @param       channel      PWM通道
 * @param       pin          PWM引脚
 * @param       arr          自动重装载值
 * @param       psc          预分频值
 * @param       alternate    复用功能
 * @note        无
 * @retval      无
 */
void tim_pwm_pin_init(uint8_t tim_id, uint8_t channel, uint8_t pin, uint32_t arr, uint32_t psc);

/**
 * @brief       设置PWM比较值
 * @param       tim_id       TIM ID号
 * @param       channel      PWM通道
 * @param       ccr          比较值
 * @note        无
 * @retval      无
 */
void tim_pwm_set_ccr(uint8_t tim_id, uint8_t channel, uint32_t ccr);

#endif
