#ifndef __OS_ABSTRACT_H__
#define __OS_ABSTRACT_H__

/* 定义任务优先级 */
#define OS_TASK_PRIORITY_IDLE 0
#define OS_TASK_PRIORITY_LOWEST 1
#define OS_TASK_PRIORITY_BELOW_NORMAL 2
#define OS_TASK_PRIORITY_NORMAL 3
#define OS_TASK_PRIORITY_ABOVE_NORMAL 4
#define OS_TASK_PRIORITY_HIGH 5
#define OS_TASK_PRIORITY_HIGHEST 6
#define OS_TASK_PRIORITY_REALTIME 7

#include "./config/board_config.h"

#if TCFG_OS == TCFG_OS_FREE_RTOS

#include "FreeRTOS.h"
#include "task.h"
#include "queue.h"
#include "semphr.h"
#include "timers.h"

/* 自定义类型映射到FreeRTOS类型 */
typedef TaskHandle_t os_task_handle_t;
typedef SemaphoreHandle_t os_sem_handle_t;
typedef SemaphoreHandle_t os_mutex_handle_t;
typedef QueueHandle_t os_queue_handle_t;
typedef TickType_t os_tick;
typedef TimerHandle_t os_timer_handle_t;

#define os_wait_max portMAX_DELAY

/* 任务相关操作 */
#define os_task_create(function, name, stack_size, arg, priority, handle) xTaskCreate((function), (name), (stack_size), (arg), (priority), (handle))
#define os_task_delete(handle) vTaskDelete((handle))
#define os_task_delay(ticks) vTaskDelay((ticks))
#define os_task_delay_until(wake_time, ticks) vTaskDelayUntil((wake_time), (ticks))
#define os_task_get_tick() xTaskGetTickCount()

/* 信号量相关操作 */
#define os_sem_create(max_count, initial_count) xSemaphoreCreateCounting((max_count), (initial_count))
#define os_sem_create_binary() xSemaphoreCreateBinary()
#define os_sem_take(sem_handle, ticks) xSemaphoreTake((sem_handle), (ticks))
#define os_sem_give(sem_handle) xSemaphoreGive((sem_handle))

/* 互斥量相关操作 */
#define os_mutex_create() xSemaphoreCreateMutex()
#define os_mutex_take(mutex_handle, ticks) xSemaphoreTake((mutex_handle), (ticks))
#define os_mutex_give(mutex_handle) xSemaphoreGive((mutex_handle))

/* 队列相关操作 */
#define os_queue_create(max_items, item_size) xQueueCreate((max_items), (item_size))
#define os_queue_send(queue_handle, item, ticks) xQueueSend((queue_handle), (item), (ticks))
#define os_queue_receive(queue_handle, item, ticks) xQueueReceive((queue_handle), (item), (ticks))

/* 软件定时器相关操作 */
#define os_timer_create(name, func, id, tick) xTimerCreate((name), pdMS_TO_TICKS(tick), pdTRUE, (id), (func))
#define os_timer_start(timer_handle, ticks) xTimerStart((timer_handle), (ticks))
#define os_timer_stop(timer_handle, ticks) xTimerStop((timer_handle), (ticks))
#define os_timer_delete(timer_handle, ticks) xTimerDelete((timer_handle), (ticks))
#define os_timer_isactive(timer_handle) xTimerIsTimerActive((timer_handle))
#define os_timer_changeperiod(timer_handle, period, ticks) xTimerChangePeriod((timer_handle), (period), (ticks))
#define os_timer_getremaining(timer_handle, ticks) xTimerGetExpiryTime((timer_handle)) - xTaskGetTickCount()

/* 启动调度器 */
#define os_start() vTaskStartScheduler()

extern void xPortPendSVHandler(void);
extern void xPortSysTickHandler(void);
extern void vPortSVCHandler(void);

#elif TCFG_OS == TCFG_OS_RTT

#else

#error "Unsupported OS"

#endif

#endif /* __OS_ABSTRACT_H__ */
