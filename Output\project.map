Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to uart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to uart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to uart.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_iwdg.o(i.HAL_IWDG_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to sys.o(i.sys_stm32_clock_init) for sys_stm32_clock_init
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to sdk_main.o(i.sdk_init) for sdk_init
    stm32f4xx_it.o(i.PendSV_Handler) refers to port.o(.emb_text) for xPortPendSVHandler
    stm32f4xx_it.o(i.SVC_Handler) refers to port.o(.emb_text) for vPortSVCHandler
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.SysTick_Handler) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    stm32f4xx_it.o(i.SysTick_Handler) refers to port.o(i.xPortSysTickHandler) for xPortSysTickHandler
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    sys.o(i.sys_check_rst) refers to debug.o(i.debug_message) for debug_message
    sys.o(i.sys_check_rst) refers to sys.o(.constdata) for .constdata
    sys.o(i.sys_get_tick) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    sys.o(i.sys_stm32_clock_init) refers to memseta.o(.text) for __aeabi_memclr4
    sys.o(i.sys_stm32_clock_init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    sys.o(i.sys_stm32_clock_init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    sys.o(i.sys_stm32_clock_init) refers to stm32f4xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    delay.o(i.HAL_Delay) refers to delay.o(i.delay_ms) for delay_ms
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    debug.o(i.debug_error) refers to debug.o(i.get_debug_time) for get_debug_time
    debug.o(i.debug_error) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.debug_error) refers to strlen.o(.text) for strlen
    debug.o(i.debug_error) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_error) refers to debug.o(i.debug_send_data) for debug_send_data
    debug.o(i.debug_error) refers to debug.o(.bss) for .bss
    debug.o(i.debug_init) refers to uart.o(i.uart_init) for uart_init
    debug.o(i.debug_init) refers to uart.o(i.uart_add_recv_callback) for uart_add_recv_callback
    debug.o(i.debug_init) refers to debug.o(i.debug_recv_callback) for debug_recv_callback
    debug.o(i.debug_message) refers to debug.o(i.get_debug_time) for get_debug_time
    debug.o(i.debug_message) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.debug_message) refers to strlen.o(.text) for strlen
    debug.o(i.debug_message) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_message) refers to debug.o(i.debug_send_data) for debug_send_data
    debug.o(i.debug_message) refers to debug.o(.bss) for .bss
    debug.o(i.debug_printf) refers to debug.o(i.get_debug_time) for get_debug_time
    debug.o(i.debug_printf) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.debug_printf) refers to strlen.o(.text) for strlen
    debug.o(i.debug_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_printf) refers to debug.o(i.debug_send_data) for debug_send_data
    debug.o(i.debug_printf) refers to debug.o(.bss) for .bss
    debug.o(i.debug_recv_callback) refers to debug.o(i.debug_message) for debug_message
    debug.o(i.debug_recv_callback) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.debug_recv_callback) refers to debug.o(.data) for .data
    debug.o(i.debug_recv_callback) refers to debug.o(.bss) for .bss
    debug.o(i.debug_recv_callback) refers to debug.o(.constdata) for .constdata
    debug.o(i.debug_send_data) refers to uart.o(i.uart_send_data) for uart_send_data
    debug.o(i.debug_test) refers to debug.o(i.debug_printf) for debug_printf
    debug.o(i.debug_test) refers to debug.o(i.debug_error) for debug_error
    debug.o(i.debug_test) refers to debug.o(i.debug_warning) for debug_warning
    debug.o(i.debug_test) refers to debug.o(i.debug_message) for debug_message
    debug.o(i.debug_test) refers to debug.o(.constdata) for .constdata
    debug.o(i.debug_warning) refers to debug.o(i.get_debug_time) for get_debug_time
    debug.o(i.debug_warning) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.debug_warning) refers to strlen.o(.text) for strlen
    debug.o(i.debug_warning) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_warning) refers to debug.o(i.debug_send_data) for debug_send_data
    debug.o(i.debug_warning) refers to debug.o(.bss) for .bss
    debug.o(i.get_debug_time) refers to sys.o(i.sys_get_tick) for sys_get_tick
    debug.o(i.get_debug_time) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.get_debug_time) refers to debug.o(.bss) for .bss
    app_event.o(i.app_event_init) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    app_event.o(i.app_event_init) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    app_event.o(i.app_event_init) refers to app_event.o(.data) for .data
    app_event.o(i.app_event_init) refers to app_event.o(i.app_process_msg) for app_process_msg
    app_event.o(i.app_msg_get_notify) refers to queue.o(i.xQueueReceive) for xQueueReceive
    app_event.o(i.app_msg_get_notify) refers to app_event.o(.data) for .data
    app_event.o(i.app_msg_handle) refers to debug.o(i.debug_message) for debug_message
    app_event.o(i.app_msg_handle) refers to debug.o(i.debug_warning) for debug_warning
    app_event.o(i.app_msg_handle) refers to debug.o(i.debug_printf) for debug_printf
    app_event.o(i.app_msg_handle) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    app_event.o(i.app_msg_handle) refers to app_event.o(.constdata) for .constdata
    app_event.o(i.app_msg_send_notify) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    app_event.o(i.app_msg_send_notify) refers to app_event.o(.data) for .data
    app_event.o(i.app_process_msg) refers to app_event.o(i.app_msg_get_notify) for app_msg_get_notify
    app_event.o(i.app_process_msg) refers to app_event.o(i.app_msg_handle) for app_msg_handle
    system_event.o(i.process_sys_event) refers to key.o(i.process_key_event) for process_key_event
    system_event.o(i.process_sys_event) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    system_event.o(i.process_sys_event) refers to system_event.o(i.sys_event_get_notify) for sys_event_get_notify
    system_event.o(i.sys_event_get_notify) refers to queue.o(i.xQueueReceive) for xQueueReceive
    system_event.o(i.sys_event_get_notify) refers to system_event.o(.data) for .data
    system_event.o(i.sys_event_init) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    system_event.o(i.sys_event_init) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    system_event.o(i.sys_event_init) refers to system_event.o(.data) for .data
    system_event.o(i.sys_event_init) refers to system_event.o(i.process_sys_event) for process_sys_event
    system_event.o(i.sys_event_send_notify) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    system_event.o(i.sys_event_send_notify) refers to system_event.o(.data) for .data
    sdk_main.o(i.app_init) refers to app_event.o(i.app_event_init) for app_event_init
    sdk_main.o(i.board_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    sdk_main.o(i.board_init) refers to key.o(i.key_init) for key_init
    sdk_main.o(i.board_init) refers to sdk_main.o(i.display_show_init) for display_show_init
    sdk_main.o(i.display_show_init) refers to driver_oled.o(i.OLED_Init) for OLED_Init
    sdk_main.o(i.get_temperature_mcu) refers to adc.o(i.adc_get_value) for adc_get_value
    sdk_main.o(i.get_temperature_mcu) refers to dfltui.o(.text) for __aeabi_ui2d
    sdk_main.o(i.get_temperature_mcu) refers to dmul.o(.text) for __aeabi_dmul
    sdk_main.o(i.get_temperature_mcu) refers to d2f.o(.text) for __aeabi_d2f
    sdk_main.o(i.iwdg_feed_task) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    sdk_main.o(i.iwdg_feed_task) refers to tasks.o(i.xTaskDelayUntil) for xTaskDelayUntil
    sdk_main.o(i.iwdg_feed_task) refers to wdg.o(i.iwdg_feed) for iwdg_feed
    sdk_main.o(i.iwdg_feed_task) refers to sdk_main.o(.data) for .data
    sdk_main.o(i.sdk_init) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    sdk_main.o(i.sdk_init) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    sdk_main.o(i.sdk_init) refers to sdk_main.o(i.sdk_main) for sdk_main
    sdk_main.o(i.sdk_init) refers to sdk_main.o(i.iwdg_feed_task) for iwdg_feed_task
    sdk_main.o(i.sdk_main) refers to sdk_main.o(i.system_init) for system_init
    sdk_main.o(i.sdk_main) refers to sdk_main.o(i.board_init) for board_init
    sdk_main.o(i.sdk_main) refers to sdk_main.o(i.app_init) for app_init
    sdk_main.o(i.sdk_main) refers to device.o(i._sys_device_do_config_level) for _sys_device_do_config_level
    sdk_main.o(i.sdk_main) refers to tim.o(i.tim_pwm_pin_init) for tim_pwm_pin_init
    sdk_main.o(i.sdk_main) refers to tim.o(i.tim_pwm_set_ccr) for tim_pwm_set_ccr
    sdk_main.o(i.sdk_main) refers to debug.o(i.debug_message) for debug_message
    sdk_main.o(i.sdk_main) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    sdk_main.o(i.sdk_main) refers to debug.o(i.debug_printf) for debug_printf
    sdk_main.o(i.sdk_main) refers to adc.o(i.adc_get_value) for adc_get_value
    sdk_main.o(i.sdk_main) refers to sdk_main.o(i.get_temperature_mcu) for get_temperature_mcu
    sdk_main.o(i.sdk_main) refers to f2d.o(.text) for __aeabi_f2d
    sdk_main.o(i.sdk_main) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    sdk_main.o(i.sdk_main) refers to tasks.o(i.xTaskDelayUntil) for xTaskDelayUntil
    sdk_main.o(i.sdk_main) refers to driver_oled.o(i.OLED_Printf) for OLED_Printf
    sdk_main.o(i.sdk_main) refers to driver_oled.o(i.OLED_Update) for OLED_Update
    sdk_main.o(i.sdk_main) refers to sdk_main.o(.constdata) for .constdata
    sdk_main.o(i.sdk_main) refers to sdk_main.o(.data) for .data
    sdk_main.o(i.system_init) refers to debug.o(i.debug_init) for debug_init
    sdk_main.o(i.system_init) refers to sys.o(i.sys_check_rst) for sys_check_rst
    sdk_main.o(i.system_init) refers to system_event.o(i.sys_event_init) for sys_event_init
    sdk_main.o(i.system_init) refers to adc.o(i.adc_pin_init) for adc_pin_init
    event_groups.o(i.vEventGroupClearBitsCallback) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupSetBitsCallback) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to event_groups.o(i.prvTestWaitCondition) for prvTestWaitCondition
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvCopyDataFromQueue) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.prvInitialiseNewQueue) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvIsQueueFull) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueFull) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to tasks.o(i.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(i.vQueueWaitForMessageRestricted) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvIsQueueFull) for prvIsQueueFull
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSendFromISR) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeek) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeekFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceive) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceiveFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to memseta.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.prvReadBytesFromBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvReadMessageFromBuffer) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.prvWriteBytesToBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvWriteMessageToBuffer) refers to stream_buffer.o(i.prvWriteBytesToBuffer) for prvWriteBytesToBuffer
    stream_buffer.o(i.vStreamBufferDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    stream_buffer.o(i.vStreamBufferDelete) refers to memseta.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.xStreamBufferBytesAvailable) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferIsFull) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotifyStateClear) for xTaskGenericNotifyStateClear
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotifyWait) for xTaskGenericNotifyWait
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReset) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferResetFromISR) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotifyStateClear) for xTaskGenericNotifyStateClear
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotifyWait) for xTaskGenericNotifyWait
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferSendCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(i.prvInitialiseTaskLists) for prvInitialiseTaskLists
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvCheckTasksWaitingTermination) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvCheckTasksWaitingTermination) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvCheckTasksWaitingTermination) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(.data) for .data
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvCreateIdleTasks) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    tasks.o(i.prvCreateIdleTasks) refers to tasks.o(.data) for .data
    tasks.o(i.prvCreateIdleTasks) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.prvCreateTask) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.prvCreateTask) refers to memseta.o(.text) for __aeabi_memclr4
    tasks.o(i.prvCreateTask) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvCreateTask) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.prvDeleteTCB) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to tasks.o(i.prvCheckTasksWaitingTermination) for prvCheckTasksWaitingTermination
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvInitialiseNewTask) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.prvInitialiseNewTask) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.prvInitialiseTaskLists) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.prvInitialiseTaskLists) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvInitialiseTaskLists) refers to tasks.o(.data) for .data
    tasks.o(i.prvResetNextTaskUnblockTime) refers to tasks.o(.data) for .data
    tasks.o(i.prvTaskIsTaskSuspended) refers to tasks.o(.bss) for .bss
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.ulTaskGenericNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskGenericNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.uxTaskPriorityGet) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskPriorityGetFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelay) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskEndScheduler) refers to timers.o(i.xTimerGetTimerDaemonTaskHandle) for xTimerGetTimerDaemonTaskHandle
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(i.prvCheckTasksWaitingTermination) for prvCheckTasksWaitingTermination
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskResetState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskResume) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskResume) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.vTaskResume) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskResume) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskResume) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskResume) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvCreateIdleTasks) for prvCreateIdleTasks
    tasks.o(i.vTaskStartScheduler) refers to timers.o(i.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSuspend) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskSuspend) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    tasks.o(i.vTaskSuspend) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.xTaskCatchUpTicks) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCatchUpTicks) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvCreateTask) for prvCreateTask
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotifyStateClear) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.xTaskGenericNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetCurrentTaskHandleForCore) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.bss) for .bss
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.data) for .data
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.bss) for .bss
    timers.o(i.prvGetNextExpireTime) refers to timers.o(.data) for .data
    timers.o(i.prvInitialiseNewTimer) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.prvInitialiseNewTimer) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    timers.o(i.prvInsertTimerInActiveList) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvInsertTimerInActiveList) refers to timers.o(.data) for .data
    timers.o(i.prvProcessExpiredTimer) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessExpiredTimer) refers to timers.o(i.prvReloadTimer) for prvReloadTimer
    timers.o(i.prvProcessExpiredTimer) refers to timers.o(.data) for .data
    timers.o(i.prvProcessReceivedCommands) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvReloadTimer) for prvReloadTimer
    timers.o(i.prvProcessReceivedCommands) refers to heap_4.o(i.vPortFree) for vPortFree
    timers.o(i.prvProcessReceivedCommands) refers to queue.o(i.xQueueReceive) for xQueueReceive
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(.data) for .data
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvProcessExpiredTimer) for prvProcessExpiredTimer
    timers.o(i.prvProcessTimerOrBlockTask) refers to queue.o(i.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(.data) for .data
    timers.o(i.prvReloadTimer) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvSampleTimeNow) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvSampleTimeNow) refers to timers.o(i.prvSwitchTimerLists) for prvSwitchTimerLists
    timers.o(i.prvSampleTimeNow) refers to timers.o(.data) for .data
    timers.o(i.prvSwitchTimerLists) refers to timers.o(i.prvProcessExpiredTimer) for prvProcessExpiredTimer
    timers.o(i.prvSwitchTimerLists) refers to timers.o(.data) for .data
    timers.o(i.prvTimerTask) refers to timers.o(i.prvGetNextExpireTime) for prvGetNextExpireTime
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessTimerOrBlockTask) for prvProcessTimerOrBlockTask
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessReceivedCommands) for prvProcessReceivedCommands
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.uxTimerGetReloadMode) refers to timers.o(i.xTimerGetReloadMode) for xTimerGetReloadMode
    timers.o(i.vTimerResetState) refers to timers.o(.data) for .data
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    timers.o(i.xTimerCreate) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.xTimerCreateTimerTask) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.data) for .data
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvTimerTask) for prvTimerTask
    timers.o(i.xTimerGenericCommandFromISR) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerGenericCommandFromISR) refers to timers.o(.data) for .data
    timers.o(i.xTimerGenericCommandFromTask) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(i.xTimerGenericCommandFromTask) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerGenericCommandFromTask) refers to timers.o(.data) for .data
    timers.o(i.xTimerGetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerGetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.data) for .data
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.bss) for .bss
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.data) for .data
    heap_4.o(i.prvInsertBlockIntoFreeList) refers to heap_4.o(.data) for .data
    heap_4.o(i.pvPortCalloc) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    heap_4.o(i.pvPortCalloc) refers to memseta.o(.text) for __aeabi_memclr
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvHeapInit) for prvHeapInit
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.vPortGetHeapStats) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortHeapResetState) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for .data
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEnterCritical) refers to port.o(.data) for .data
    port.o(i.vPortExitCritical) refers to port.o(.data) for .data
    port.o(i.xPortStartScheduler) refers to port.o(i.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvEnableVFP
    port.o(i.xPortStartScheduler) refers to port.o(.data) for .data
    port.o(i.xPortSysTickHandler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    key.o(i.process_key_event) refers to debug.o(i.debug_message) for debug_message
    key.o(i.process_key_event) refers to key.o(.constdata) for .constdata
    device.o(i._sys_device_do_config_level) refers to device.o(.data) for .data
    device.o(i.device_get_binding) refers to strcmp.o(.text) for strcmp
    device.o(i.device_get_binding) refers to device.o(.data) for .data
    gpio_led.o(i.gpio_led1_on) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    gpio_led.o(i.gpio_led_blue) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    gpio_led.o(i.gpio_led_green) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    gpio_led.o(i.gpio_led_init) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    gpio_led.o(i.gpio_led_init) refers to gpio_led.o(i.gpio_led_run) for gpio_led_run
    gpio_led.o(i.gpio_led_off) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    gpio_led.o(i.gpio_led_red) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    gpio_led.o(i.gpio_led_run) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    gpio_led.o(i.gpio_led_run) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    gpio_led.o(.data) refers to gpio_led.o(i.gpio_led_red) for gpio_led_red
    gpio_led.o(.data) refers to gpio_led.o(i.gpio_led_green) for gpio_led_green
    gpio_led.o(.data) refers to gpio_led.o(i.gpio_led_blue) for gpio_led_blue
    gpio_led.o(.data) refers to gpio_led.o(i.gpio_led_off) for gpio_led_off
    gpio_led.o(.devconfig.init) refers to gpio_led.o(.conststrlit) for .conststrlit
    gpio_led.o(.devconfig.init) refers to gpio_led.o(i.gpio_led_init) for gpio_led_init
    gpio_led.o(.devconfig.init) refers to gpio_led.o(.data) for _gpio_led_config
    gpio_led.o(.device_DRIVER) refers to gpio_led.o(.devconfig.init) for __config_gpio_led
    gpio_led.o(.device_DRIVER) refers to gpio_led.o(.data) for _gpio_led_api
    adc.o(i.adc_get_value) refers to adc.o(.bss) for .bss
    adc.o(i.adc_get_value) refers to adc.o(.data) for .data
    adc.o(i.adc_pin_init) refers to debug.o(i.debug_error) for debug_error
    adc.o(i.adc_pin_init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.adc_pin_init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.adc_pin_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    adc.o(i.adc_pin_init) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    adc.o(i.adc_pin_init) refers to adc.o(.bss) for .bss
    adc.o(i.adc_pin_init) refers to adc.o(.data) for .data
    adc.o(i.adc_pin_init) refers to adc.o(.constdata) for .constdata
    adc.o(i.adc_pin_init) refers to adc.o(i.adc_read_task) for adc_read_task
    adc.o(i.adc_read_task) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    adc.o(i.adc_read_task) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) for HAL_ADC_PollForConversion
    adc.o(i.adc_read_task) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    adc.o(i.adc_read_task) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Stop) for HAL_ADC_Stop
    adc.o(i.adc_read_task) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    adc.o(i.adc_read_task) refers to adc.o(.data) for .data
    adc.o(i.adc_read_task) refers to adc.o(.bss) for .bss
    gpio.o(i.gpio_pin_init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.gpio_pin_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.gpio_pin_init) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_pin_init_alternate) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.gpio_pin_init_alternate) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.gpio_pin_init_alternate) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_read_pin) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    gpio.o(i.gpio_read_pin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_toggle_pin) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    gpio.o(i.gpio_toggle_pin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_write_pin) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.gpio_write_pin) refers to gpio.o(.constdata) for .constdata
    i2c.o(i.i2c_ack) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_ack) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_ack) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_delay) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    i2c.o(i.i2c_init) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_nack) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_nack) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_nack) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_read_byte) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_read_byte) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_read_byte) refers to gpio.o(i.gpio_read_pin) for gpio_read_pin
    i2c.o(i.i2c_read_byte) refers to i2c.o(i.i2c_ack) for i2c_ack
    i2c.o(i.i2c_read_byte) refers to i2c.o(i.i2c_nack) for i2c_nack
    i2c.o(i.i2c_read_byte) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_send_byte) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_send_byte) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_send_byte) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_start) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_start) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_start) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_stop) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_stop) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_stop) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_wait_ack) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_wait_ack) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_wait_ack) refers to i2c.o(i.i2c_stop) for i2c_stop
    i2c.o(i.i2c_wait_ack) refers to gpio.o(i.gpio_read_pin) for gpio_read_pin
    i2c.o(i.i2c_wait_ack) refers to i2c.o(.data) for .data
    spi.o(i.spi_pin_init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.spi_pin_init) refers to gpio.o(i.gpio_pin_init_alternate) for gpio_pin_init_alternate
    spi.o(i.spi_pin_init) refers to spi.o(.bss) for .bss
    spi.o(i.spi_receive_data) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    spi.o(i.spi_receive_data) refers to spi.o(.bss) for .bss
    spi.o(i.spi_send_data) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    spi.o(i.spi_send_data) refers to spi.o(.bss) for .bss
    tim.o(i.tim_pwm_pin_init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.tim_pwm_pin_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.tim_pwm_pin_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.tim_pwm_pin_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    tim.o(i.tim_pwm_pin_init) refers to gpio.o(i.gpio_pin_init_alternate) for gpio_pin_init_alternate
    tim.o(i.tim_pwm_pin_init) refers to tim.o(.bss) for .bss
    tim.o(i.tim_pwm_set_ccr) refers to tim.o(.bss) for .bss
    uart.o(i.USART1_IRQHandler) refers to uart.o(.bss) for .bss
    uart.o(i.USART2_IRQHandler) refers to uart.o(.bss) for .bss
    uart.o(i.USART3_IRQHandler) refers to uart.o(.bss) for .bss
    uart.o(i.uart_add_recv_callback) refers to uart.o(.bss) for .bss
    uart.o(i.uart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    uart.o(i.uart_init) refers to gpio.o(i.gpio_pin_init_alternate) for gpio_pin_init_alternate
    uart.o(i.uart_init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    uart.o(i.uart_init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    uart.o(i.uart_init) refers to uart.o(.bss) for .bss
    uart.o(i.uart_send_data) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart.o(i.uart_send_data) refers to uart.o(.bss) for .bss
    wdg.o(i.iwdg_feed) refers to stm32f4xx_hal_iwdg.o(i.HAL_IWDG_Refresh) for HAL_IWDG_Refresh
    wdg.o(i.iwdg_feed) refers to wdg.o(.bss) for .bss
    wdg.o(i.iwdg_init) refers to stm32f4xx_hal_iwdg.o(i.HAL_IWDG_Init) for HAL_IWDG_Init
    wdg.o(i.iwdg_init) refers to wdg.o(.bss) for .bss
    cjson.o(i.add_item_to_array) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.add_item_to_object) refers to cjson.o(i.cast_away_const) for cast_away_const
    cjson.o(i.add_item_to_object) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.add_item_to_object) refers to cjson.o(i.add_item_to_array) for add_item_to_array
    cjson.o(i.cJSON_AddArrayToObject) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_AddArrayToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddArrayToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddArrayToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddBoolToObject) refers to cjson.o(i.cJSON_CreateBool) for cJSON_CreateBool
    cjson.o(i.cJSON_AddBoolToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddBoolToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddBoolToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddFalseToObject) refers to cjson.o(i.cJSON_CreateFalse) for cJSON_CreateFalse
    cjson.o(i.cJSON_AddFalseToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddFalseToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddFalseToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddItemReferenceToArray) refers to cjson.o(i.create_reference) for create_reference
    cjson.o(i.cJSON_AddItemReferenceToArray) refers to cjson.o(i.add_item_to_array) for add_item_to_array
    cjson.o(i.cJSON_AddItemReferenceToArray) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddItemReferenceToObject) refers to cjson.o(i.create_reference) for create_reference
    cjson.o(i.cJSON_AddItemReferenceToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddItemReferenceToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddItemToArray) refers to cjson.o(i.add_item_to_array) for add_item_to_array
    cjson.o(i.cJSON_AddItemToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddItemToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddItemToObjectCS) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddItemToObjectCS) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddNullToObject) refers to cjson.o(i.cJSON_CreateNull) for cJSON_CreateNull
    cjson.o(i.cJSON_AddNullToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddNullToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddNullToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddNumberToObject) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    cjson.o(i.cJSON_AddNumberToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddNumberToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddNumberToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddObjectToObject) refers to cjson.o(i.cJSON_CreateObject) for cJSON_CreateObject
    cjson.o(i.cJSON_AddObjectToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddObjectToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddObjectToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddRawToObject) refers to cjson.o(i.cJSON_CreateRaw) for cJSON_CreateRaw
    cjson.o(i.cJSON_AddRawToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddRawToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddRawToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddStringToObject) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    cjson.o(i.cJSON_AddStringToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddStringToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddStringToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddTrueToObject) refers to cjson.o(i.cJSON_CreateTrue) for cJSON_CreateTrue
    cjson.o(i.cJSON_AddTrueToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddTrueToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddTrueToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_Compare) refers to cjson.o(i.compare_double) for compare_double
    cjson.o(i.cJSON_Compare) refers to strcmp.o(.text) for strcmp
    cjson.o(i.cJSON_Compare) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.cJSON_CreateArray) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateArray) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateArrayReference) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateArrayReference) refers to cjson.o(i.cast_away_const) for cast_away_const
    cjson.o(i.cJSON_CreateArrayReference) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateBool) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateBool) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateDoubleArray) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_CreateDoubleArray) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    cjson.o(i.cJSON_CreateDoubleArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_CreateDoubleArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_CreateFalse) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateFalse) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateFloatArray) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_CreateFloatArray) refers to f2d.o(.text) for __aeabi_f2d
    cjson.o(i.cJSON_CreateFloatArray) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    cjson.o(i.cJSON_CreateFloatArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_CreateFloatArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_CreateIntArray) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_CreateIntArray) refers to dflti.o(.text) for __aeabi_i2d
    cjson.o(i.cJSON_CreateIntArray) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    cjson.o(i.cJSON_CreateIntArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_CreateIntArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_CreateNull) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateNull) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateNumber) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateNumber) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.cJSON_CreateNumber) refers to cdcmple.o(.text) for __aeabi_cdcmple
    cjson.o(i.cJSON_CreateNumber) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.cJSON_CreateNumber) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateObject) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateObjectReference) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateObjectReference) refers to cjson.o(i.cast_away_const) for cast_away_const
    cjson.o(i.cJSON_CreateObjectReference) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateRaw) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateRaw) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.cJSON_CreateRaw) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_CreateRaw) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateString) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateString) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.cJSON_CreateString) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_CreateString) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateStringArray) refers to cjson.o(i.cJSON_CreateArray) for cJSON_CreateArray
    cjson.o(i.cJSON_CreateStringArray) refers to cjson.o(i.cJSON_CreateString) for cJSON_CreateString
    cjson.o(i.cJSON_CreateStringArray) refers to cjson.o(i.suffix_object) for suffix_object
    cjson.o(i.cJSON_CreateStringArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_CreateStringReference) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateStringReference) refers to cjson.o(i.cast_away_const) for cast_away_const
    cjson.o(i.cJSON_CreateStringReference) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateTrue) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_CreateTrue) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_Delete) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_DeleteItemFromArray) refers to cjson.o(i.cJSON_DetachItemFromArray) for cJSON_DetachItemFromArray
    cjson.o(i.cJSON_DeleteItemFromArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_DeleteItemFromObject) refers to cjson.o(i.cJSON_DetachItemFromObject) for cJSON_DetachItemFromObject
    cjson.o(i.cJSON_DeleteItemFromObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_DeleteItemFromObjectCaseSensitive) refers to cjson.o(i.cJSON_DetachItemFromObjectCaseSensitive) for cJSON_DetachItemFromObjectCaseSensitive
    cjson.o(i.cJSON_DeleteItemFromObjectCaseSensitive) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_DetachItemFromArray) refers to cjson.o(i.get_array_item) for get_array_item
    cjson.o(i.cJSON_DetachItemFromArray) refers to cjson.o(i.cJSON_DetachItemViaPointer) for cJSON_DetachItemViaPointer
    cjson.o(i.cJSON_DetachItemFromObject) refers to cjson.o(i.cJSON_GetObjectItem) for cJSON_GetObjectItem
    cjson.o(i.cJSON_DetachItemFromObject) refers to cjson.o(i.cJSON_DetachItemViaPointer) for cJSON_DetachItemViaPointer
    cjson.o(i.cJSON_DetachItemFromObjectCaseSensitive) refers to cjson.o(i.cJSON_GetObjectItemCaseSensitive) for cJSON_GetObjectItemCaseSensitive
    cjson.o(i.cJSON_DetachItemFromObjectCaseSensitive) refers to cjson.o(i.cJSON_DetachItemViaPointer) for cJSON_DetachItemViaPointer
    cjson.o(i.cJSON_Duplicate) refers to cjson.o(i.cJSON_Duplicate_rec) for cJSON_Duplicate_rec
    cjson.o(i.cJSON_Duplicate_rec) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_Duplicate_rec) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.cJSON_Duplicate_rec) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_Duplicate_rec) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_GetArrayItem) refers to cjson.o(i.get_array_item) for get_array_item
    cjson.o(i.cJSON_GetErrorPtr) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_GetNumberValue) refers to cjson.o(i.cJSON_IsNumber) for cJSON_IsNumber
    cjson.o(i.cJSON_GetObjectItem) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.cJSON_GetObjectItemCaseSensitive) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.cJSON_GetStringValue) refers to cjson.o(i.cJSON_IsString) for cJSON_IsString
    cjson.o(i.cJSON_HasObjectItem) refers to cjson.o(i.cJSON_GetObjectItem) for cJSON_GetObjectItem
    cjson.o(i.cJSON_InitHooks) refers to mallocr.o(i.realloc) for realloc
    cjson.o(i.cJSON_InitHooks) refers to mallocr.o(i.__malloc$realloc) for malloc
    cjson.o(i.cJSON_InitHooks) refers to mallocr.o(i.__free$realloc) for free
    cjson.o(i.cJSON_InitHooks) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_InsertItemInArray) refers to cjson.o(i.get_array_item) for get_array_item
    cjson.o(i.cJSON_InsertItemInArray) refers to cjson.o(i.add_item_to_array) for add_item_to_array
    cjson.o(i.cJSON_Minify) refers to cjson.o(i.skip_oneline_comment) for skip_oneline_comment
    cjson.o(i.cJSON_Minify) refers to cjson.o(i.skip_multiline_comment) for skip_multiline_comment
    cjson.o(i.cJSON_Minify) refers to cjson.o(i.minify_string) for minify_string
    cjson.o(i.cJSON_New_Item) refers to memseta.o(.text) for __aeabi_memclr4
    cjson.o(i.cJSON_Parse) refers to cjson.o(i.cJSON_ParseWithOpts) for cJSON_ParseWithOpts
    cjson.o(i.cJSON_ParseWithLength) refers to cjson.o(i.cJSON_ParseWithLengthOpts) for cJSON_ParseWithLengthOpts
    cjson.o(i.cJSON_ParseWithLengthOpts) refers to memseta.o(.text) for __aeabi_memclr4
    cjson.o(i.cJSON_ParseWithLengthOpts) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.cJSON_ParseWithLengthOpts) refers to cjson.o(i.skip_utf8_bom) for skip_utf8_bom
    cjson.o(i.cJSON_ParseWithLengthOpts) refers to cjson.o(i.buffer_skip_whitespace) for buffer_skip_whitespace
    cjson.o(i.cJSON_ParseWithLengthOpts) refers to cjson.o(i.parse_value) for parse_value
    cjson.o(i.cJSON_ParseWithLengthOpts) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_ParseWithLengthOpts) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_ParseWithOpts) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_ParseWithOpts) refers to cjson.o(i.cJSON_ParseWithLengthOpts) for cJSON_ParseWithLengthOpts
    cjson.o(i.cJSON_Print) refers to cjson.o(i.print) for print
    cjson.o(i.cJSON_Print) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_PrintBuffered) refers to memseta.o(.text) for __aeabi_memclr4
    cjson.o(i.cJSON_PrintBuffered) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.cJSON_PrintBuffered) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_PrintPreallocated) refers to memseta.o(.text) for __aeabi_memclr4
    cjson.o(i.cJSON_PrintPreallocated) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.cJSON_PrintPreallocated) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_PrintUnformatted) refers to cjson.o(i.print) for print
    cjson.o(i.cJSON_PrintUnformatted) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_ReplaceItemInArray) refers to cjson.o(i.get_array_item) for get_array_item
    cjson.o(i.cJSON_ReplaceItemInArray) refers to cjson.o(i.cJSON_ReplaceItemViaPointer) for cJSON_ReplaceItemViaPointer
    cjson.o(i.cJSON_ReplaceItemInObject) refers to cjson.o(i.replace_item_in_object) for replace_item_in_object
    cjson.o(i.cJSON_ReplaceItemInObjectCaseSensitive) refers to cjson.o(i.replace_item_in_object) for replace_item_in_object
    cjson.o(i.cJSON_ReplaceItemViaPointer) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_SetNumberHelper) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.cJSON_SetNumberHelper) refers to cdcmple.o(.text) for __aeabi_cdcmple
    cjson.o(i.cJSON_SetNumberHelper) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.cJSON_SetValuestring) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_SetValuestring) refers to strcpy.o(.text) for strcpy
    cjson.o(i.cJSON_SetValuestring) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.cJSON_SetValuestring) refers to cjson.o(i.cJSON_free) for cJSON_free
    cjson.o(i.cJSON_SetValuestring) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_Version) refers to printfa.o(i.__0sprintf) for __2sprintf
    cjson.o(i.cJSON_Version) refers to cjson.o(.bss) for .bss
    cjson.o(i.cJSON_free) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_malloc) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_strdup) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_strdup) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.case_insensitive_strcmp) refers to tolower.o(.text) for tolower
    cjson.o(i.compare_double) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    cjson.o(i.compare_double) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.compare_double) refers to dmul.o(.text) for __aeabi_dmul
    cjson.o(i.compare_double) refers to dadd.o(.text) for __aeabi_dsub
    cjson.o(i.compare_double) refers to cdcmple.o(.text) for __aeabi_cdcmple
    cjson.o(i.create_reference) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.create_reference) refers to memcpya.o(.text) for __aeabi_memcpy4
    cjson.o(i.ensure) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.get_object_item) refers to strcmp.o(.text) for strcmp
    cjson.o(i.get_object_item) refers to cjson.o(i.case_insensitive_strcmp) for case_insensitive_strcmp
    cjson.o(i.parse_array) refers to cjson.o(i.buffer_skip_whitespace) for buffer_skip_whitespace
    cjson.o(i.parse_array) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.parse_array) refers to cjson.o(i.parse_value) for parse_value
    cjson.o(i.parse_array) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.parse_number) refers to cjson.o(i.get_decimal_point) for get_decimal_point
    cjson.o(i.parse_number) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.parse_number) refers to strtod.o(i.__hardfp_strtod) for __hardfp_strtod
    cjson.o(i.parse_number) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.parse_number) refers to cdcmple.o(.text) for __aeabi_cdcmple
    cjson.o(i.parse_number) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.parse_object) refers to cjson.o(i.buffer_skip_whitespace) for buffer_skip_whitespace
    cjson.o(i.parse_object) refers to cjson.o(i.cJSON_New_Item) for cJSON_New_Item
    cjson.o(i.parse_object) refers to cjson.o(i.parse_string) for parse_string
    cjson.o(i.parse_object) refers to cjson.o(i.parse_value) for parse_value
    cjson.o(i.parse_object) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.parse_string) refers to cjson.o(i.utf16_literal_to_utf8) for utf16_literal_to_utf8
    cjson.o(i.parse_value) refers to strncmp.o(.text) for strncmp
    cjson.o(i.parse_value) refers to cjson.o(i.parse_number) for parse_number
    cjson.o(i.parse_value) refers to cjson.o(i.parse_string) for parse_string
    cjson.o(i.parse_value) refers to cjson.o(i.parse_array) for parse_array
    cjson.o(i.parse_value) refers to cjson.o(i.parse_object) for parse_object
    cjson.o(i.print) refers to memseta.o(.text) for __aeabi_memclr4
    cjson.o(i.print) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.print) refers to cjson.o(i.update_offset) for update_offset
    cjson.o(i.print) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.print_array) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_array) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.print_array) refers to cjson.o(i.update_offset) for update_offset
    cjson.o(i.print_number) refers to scanf_fp.o(.text) for _scanf_real
    cjson.o(i.print_number) refers to memseta.o(.text) for __aeabi_memclr4
    cjson.o(i.print_number) refers to cjson.o(i.get_decimal_point) for get_decimal_point
    cjson.o(i.print_number) refers to printfa.o(i.__0sprintf) for __2sprintf
    cjson.o(i.print_number) refers to dflti.o(.text) for __aeabi_i2d
    cjson.o(i.print_number) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    cjson.o(i.print_number) refers to __0sscanf.o(.text) for __0sscanf
    cjson.o(i.print_number) refers to cjson.o(i.compare_double) for compare_double
    cjson.o(i.print_number) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_object) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_object) refers to cjson.o(i.print_string_ptr) for print_string_ptr
    cjson.o(i.print_object) refers to cjson.o(i.update_offset) for update_offset
    cjson.o(i.print_object) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.print_string) refers to cjson.o(i.print_string_ptr) for print_string_ptr
    cjson.o(i.print_string_ptr) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_string_ptr) refers to strcpy.o(.text) for strcpy
    cjson.o(i.print_string_ptr) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.print_string_ptr) refers to printfa.o(i.__0sprintf) for __2sprintf
    cjson.o(i.print_value) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_value) refers to strcpy.o(.text) for strcpy
    cjson.o(i.print_value) refers to cjson.o(i.print_number) for print_number
    cjson.o(i.print_value) refers to strlen.o(.text) for strlen
    cjson.o(i.print_value) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.print_value) refers to cjson.o(i.print_string) for print_string
    cjson.o(i.print_value) refers to cjson.o(i.print_array) for print_array
    cjson.o(i.print_value) refers to cjson.o(i.print_object) for print_object
    cjson.o(i.replace_item_in_object) refers to cjson.o(i.cJSON_free) for cJSON_free
    cjson.o(i.replace_item_in_object) refers to cjson.o(i.cJSON_strdup) for cJSON_strdup
    cjson.o(i.replace_item_in_object) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.replace_item_in_object) refers to cjson.o(i.cJSON_ReplaceItemViaPointer) for cJSON_ReplaceItemViaPointer
    cjson.o(i.replace_item_in_object) refers to cjson.o(.data) for .data
    cjson.o(i.skip_utf8_bom) refers to strncmp.o(.text) for strncmp
    cjson.o(i.update_offset) refers to strlen.o(.text) for strlen
    cjson.o(i.utf16_literal_to_utf8) refers to cjson.o(i.parse_hex4) for parse_hex4
    cjson.o(.data) refers to mallocr.o(i.__malloc$realloc) for malloc
    cjson.o(.data) refers to mallocr.o(i.__free$realloc) for free
    cjson.o(.data) refers to mallocr.o(i.realloc) for realloc
    driver_oled.o(i.OLED_Clear) refers to driver_oled.o(.bss) for .bss
    driver_oled.o(i.OLED_ClearArea) refers to driver_oled.o(.bss) for .bss
    driver_oled.o(i.OLED_DrawArc) refers to driver_oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    driver_oled.o(i.OLED_DrawArc) refers to driver_oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    driver_oled.o(i.OLED_DrawCircle) refers to driver_oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    driver_oled.o(i.OLED_DrawEllipse) refers to dflti.o(.text) for __aeabi_i2d
    driver_oled.o(i.OLED_DrawEllipse) refers to dadd.o(.text) for __aeabi_dadd
    driver_oled.o(i.OLED_DrawEllipse) refers to dmul.o(.text) for __aeabi_dmul
    driver_oled.o(i.OLED_DrawEllipse) refers to d2f.o(.text) for __aeabi_d2f
    driver_oled.o(i.OLED_DrawEllipse) refers to driver_oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    driver_oled.o(i.OLED_DrawEllipse) refers to cdcmple.o(.text) for __aeabi_cdcmple
    driver_oled.o(i.OLED_DrawLine) refers to driver_oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    driver_oled.o(i.OLED_DrawPoint) refers to driver_oled.o(.bss) for .bss
    driver_oled.o(i.OLED_DrawRectangle) refers to driver_oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    driver_oled.o(i.OLED_DrawTriangle) refers to driver_oled.o(i.OLED_DrawLine) for OLED_DrawLine
    driver_oled.o(i.OLED_DrawTriangle) refers to driver_oled.o(i.OLED_pnpoly) for OLED_pnpoly
    driver_oled.o(i.OLED_DrawTriangle) refers to driver_oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    driver_oled.o(i.OLED_GPIO_Init) refers to spi.o(i.spi_pin_init) for spi_pin_init
    driver_oled.o(i.OLED_GPIO_Init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    driver_oled.o(i.OLED_GPIO_Init) refers to driver_oled.o(i.OLED_W_RES) for OLED_W_RES
    driver_oled.o(i.OLED_GPIO_Init) refers to driver_oled.o(i.OLED_W_DC) for OLED_W_DC
    driver_oled.o(i.OLED_GPIO_Init) refers to driver_oled.o(i.OLED_W_CS) for OLED_W_CS
    driver_oled.o(i.OLED_GetPoint) refers to driver_oled.o(.bss) for .bss
    driver_oled.o(i.OLED_Init) refers to driver_oled.o(i.OLED_GPIO_Init) for OLED_GPIO_Init
    driver_oled.o(i.OLED_Init) refers to driver_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    driver_oled.o(i.OLED_Init) refers to driver_oled.o(i.OLED_Clear) for OLED_Clear
    driver_oled.o(i.OLED_Init) refers to driver_oled.o(i.OLED_Update) for OLED_Update
    driver_oled.o(i.OLED_IsInAngle) refers to dflti.o(.text) for __aeabi_i2d
    driver_oled.o(i.OLED_IsInAngle) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    driver_oled.o(i.OLED_IsInAngle) refers to ddiv.o(.text) for __aeabi_ddiv
    driver_oled.o(i.OLED_IsInAngle) refers to dmul.o(.text) for __aeabi_dmul
    driver_oled.o(i.OLED_IsInAngle) refers to dfixi.o(.text) for __aeabi_d2iz
    driver_oled.o(i.OLED_Printf) refers to printfa.o(i.__0vsprintf) for vsprintf
    driver_oled.o(i.OLED_Printf) refers to driver_oled.o(i.OLED_ShowString) for OLED_ShowString
    driver_oled.o(i.OLED_Reverse) refers to driver_oled.o(.bss) for .bss
    driver_oled.o(i.OLED_ReverseArea) refers to driver_oled.o(.bss) for .bss
    driver_oled.o(i.OLED_SetCursor) refers to driver_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    driver_oled.o(i.OLED_ShowBinNum) refers to driver_oled.o(i.OLED_Pow) for OLED_Pow
    driver_oled.o(i.OLED_ShowBinNum) refers to driver_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    driver_oled.o(i.OLED_ShowChar) refers to driver_oled.o(i.OLED_ShowImage) for OLED_ShowImage
    driver_oled.o(i.OLED_ShowChar) refers to driver_oled_data.o(.constdata) for OLED_F6x8
    driver_oled.o(i.OLED_ShowChar) refers to driver_oled_data.o(.constdata) for OLED_F8x16
    driver_oled.o(i.OLED_ShowFloatNum) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    driver_oled.o(i.OLED_ShowFloatNum) refers to driver_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    driver_oled.o(i.OLED_ShowFloatNum) refers to dfixui.o(.text) for __aeabi_d2uiz
    driver_oled.o(i.OLED_ShowFloatNum) refers to dfltui.o(.text) for __aeabi_ui2d
    driver_oled.o(i.OLED_ShowFloatNum) refers to dadd.o(.text) for __aeabi_drsub
    driver_oled.o(i.OLED_ShowFloatNum) refers to driver_oled.o(i.OLED_Pow) for OLED_Pow
    driver_oled.o(i.OLED_ShowFloatNum) refers to dmul.o(.text) for __aeabi_dmul
    driver_oled.o(i.OLED_ShowFloatNum) refers to round.o(i.__hardfp_round) for __hardfp_round
    driver_oled.o(i.OLED_ShowFloatNum) refers to driver_oled.o(i.OLED_ShowNum) for OLED_ShowNum
    driver_oled.o(i.OLED_ShowHexNum) refers to driver_oled.o(i.OLED_Pow) for OLED_Pow
    driver_oled.o(i.OLED_ShowHexNum) refers to driver_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    driver_oled.o(i.OLED_ShowImage) refers to driver_oled.o(i.OLED_ClearArea) for OLED_ClearArea
    driver_oled.o(i.OLED_ShowImage) refers to driver_oled.o(.bss) for .bss
    driver_oled.o(i.OLED_ShowNum) refers to driver_oled.o(i.OLED_Pow) for OLED_Pow
    driver_oled.o(i.OLED_ShowNum) refers to driver_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    driver_oled.o(i.OLED_ShowSignedNum) refers to driver_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    driver_oled.o(i.OLED_ShowSignedNum) refers to driver_oled.o(i.OLED_Pow) for OLED_Pow
    driver_oled.o(i.OLED_ShowString) refers to driver_oled.o(i.OLED_ShowChar) for OLED_ShowChar
    driver_oled.o(i.OLED_ShowString) refers to strcmp.o(.text) for strcmp
    driver_oled.o(i.OLED_ShowString) refers to driver_oled.o(i.OLED_ShowImage) for OLED_ShowImage
    driver_oled.o(i.OLED_ShowString) refers to driver_oled_data.o(.constdata) for OLED_CF16x16
    driver_oled.o(i.OLED_Update) refers to driver_oled.o(i.OLED_SetCursor) for OLED_SetCursor
    driver_oled.o(i.OLED_Update) refers to driver_oled.o(i.OLED_WriteData) for OLED_WriteData
    driver_oled.o(i.OLED_Update) refers to driver_oled.o(.bss) for .bss
    driver_oled.o(i.OLED_UpdateArea) refers to driver_oled.o(i.OLED_SetCursor) for OLED_SetCursor
    driver_oled.o(i.OLED_UpdateArea) refers to driver_oled.o(i.OLED_WriteData) for OLED_WriteData
    driver_oled.o(i.OLED_UpdateArea) refers to driver_oled.o(.bss) for .bss
    driver_oled.o(i.OLED_W_CS) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    driver_oled.o(i.OLED_W_DC) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    driver_oled.o(i.OLED_W_RES) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    driver_oled.o(i.OLED_WriteCommand) refers to driver_oled.o(i.OLED_W_CS) for OLED_W_CS
    driver_oled.o(i.OLED_WriteCommand) refers to driver_oled.o(i.OLED_W_DC) for OLED_W_DC
    driver_oled.o(i.OLED_WriteCommand) refers to spi.o(i.spi_send_data) for spi_send_data
    driver_oled.o(i.OLED_WriteData) refers to driver_oled.o(i.OLED_W_CS) for OLED_W_CS
    driver_oled.o(i.OLED_WriteData) refers to driver_oled.o(i.OLED_W_DC) for OLED_W_DC
    driver_oled.o(i.OLED_WriteData) refers to spi.o(i.spi_send_data) for spi_send_data
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.__hardfp_round) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.__hardfp_round) refers to drnd.o(.text) for _drnd
    round.o(i.__hardfp_round) refers to dadd.o(.text) for __aeabi_dsub
    round.o(i.__hardfp_round) refers to cdcmple.o(.text) for __aeabi_cdcmple
    round.o(i.__hardfp_round) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    round.o(i.round) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.round) refers to drnd.o(.text) for _drnd
    round.o(i.round) refers to dadd.o(.text) for __aeabi_dsub
    round.o(i.round) refers to cdcmple.o(.text) for __aeabi_cdcmple
    round.o(i.round) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    strtod.o(i.__hardfp_strtod) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtod.o(i.__hardfp_strtod) refers to strtod.o(.text) for __strtod_int
    strtod.o(i.__softfp_strtod) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtod.o(i.__softfp_strtod) refers to strtod.o(.text) for __strtod_int
    strtod.o(i.strtod) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtod.o(i.strtod) refers to strtod.o(.text) for __strtod_int
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for free
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for malloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dneg.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_c.o(.text) for isspace
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_c.o(.text) for isspace
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    drnd.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to depilogue.o(.text) for _double_round
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    _scanf.o(.text) refers (Weak) to scanf_fp.o(.text) for _scanf_real
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (512 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (32 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2824 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (316 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (36 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (14 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (60 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (144 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (328 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (324 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (58 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (248 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (86 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (86 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (216 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (168 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (116 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (248 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (52 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (12 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler), (560 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (178 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (124 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError), (16 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT), (26 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Receive_IT), (200 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Transmit_IT), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (86 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (44 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam), (120 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Abort), (142 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT), (36 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (102 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (464 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Init), (212 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (286 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (84 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT), (110 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (100 bytes).
    Removing stm32f4xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt), (110 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAError), (22 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (10 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (116 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (62 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler), (306 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (124 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA), (364 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (292 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (108 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (90 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (468 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (62 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (144 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (240 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (248 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (86 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (94 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (84 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (276 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (96 bytes).
    Removing stm32f4xx_hal_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_iwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_iwdg.o(i.HAL_IWDG_Init), (90 bytes).
    Removing stm32f4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort), (296 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT), (304 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop), (68 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (260 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive), (344 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (176 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (502 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (292 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (164 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (208 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (152 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR), (88 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR), (28 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (152 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR), (76 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR), (128 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError), (16 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (108 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback), (102 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt), (100 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (90 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback), (116 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction), (140 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT), (32 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (72 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init), (74 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (212 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (232 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (234 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (452 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (452 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (180 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (168 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (496 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (208 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (170 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (234 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (210 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (36 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (72 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (74 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (272 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (516 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (340 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (114 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (208 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (312 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (72 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (74 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (492 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (288 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (258 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (72 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (492 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (288 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (80 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (80 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig), (56 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (36 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (64 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (210 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (172 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (240 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (184 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (212 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (436 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (272 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (212 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (436 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (272 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (32 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (136 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing sys.o(i.sys_soft_reset), (36 bytes).
    Removing sys.o(i.sys_standby), (40 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing delay.o(i.HAL_Delay), (6 bytes).
    Removing delay.o(i.delay_ms), (10 bytes).
    Removing delay.o(i.delay_us), (56 bytes).
    Removing debug.o(.rev16_text), (4 bytes).
    Removing debug.o(.revsh_text), (4 bytes).
    Removing debug.o(.rrx_text), (6 bytes).
    Removing debug.o(i.debug_test), (232 bytes).
    Removing app_event.o(.rev16_text), (4 bytes).
    Removing app_event.o(.revsh_text), (4 bytes).
    Removing app_event.o(.rrx_text), (6 bytes).
    Removing app_event.o(i.app_msg_send_notify), (24 bytes).
    Removing system_event.o(.rev16_text), (4 bytes).
    Removing system_event.o(.revsh_text), (4 bytes).
    Removing system_event.o(.rrx_text), (6 bytes).
    Removing system_event.o(i.sys_event_send_notify), (20 bytes).
    Removing sdk_main.o(.rev16_text), (4 bytes).
    Removing sdk_main.o(.revsh_text), (4 bytes).
    Removing sdk_main.o(.rrx_text), (6 bytes).
    Removing event_groups.o(i.prvTestWaitCondition), (24 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (4 bytes).
    Removing event_groups.o(i.vEventGroupDelete), (44 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (4 bytes).
    Removing event_groups.o(i.xEventGroupClearBits), (26 bytes).
    Removing event_groups.o(i.xEventGroupCreate), (26 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (26 bytes).
    Removing event_groups.o(i.xEventGroupSetBits), (118 bytes).
    Removing event_groups.o(i.xEventGroupSync), (136 bytes).
    Removing event_groups.o(i.xEventGroupWaitBits), (188 bytes).
    Removing list.o(i.vListInsertEnd), (24 bytes).
    Removing queue.o(i.prvCopyDataToQueue), (92 bytes).
    Removing queue.o(i.prvIsQueueFull), (30 bytes).
    Removing queue.o(i.uxQueueGetQueueItemSize), (4 bytes).
    Removing queue.o(i.uxQueueGetQueueLength), (4 bytes).
    Removing queue.o(i.uxQueueMessagesWaiting), (18 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (4 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (22 bytes).
    Removing queue.o(i.vQueueDelete), (4 bytes).
    Removing queue.o(i.xQueueGenericSend), (248 bytes).
    Removing queue.o(i.xQueueGenericSendFromISR), (112 bytes).
    Removing queue.o(i.xQueueGiveFromISR), (102 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (14 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (16 bytes).
    Removing queue.o(i.xQueuePeek), (240 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (50 bytes).
    Removing queue.o(i.xQueueReceiveFromISR), (110 bytes).
    Removing queue.o(i.xQueueSemaphoreTake), (232 bytes).
    Removing stream_buffer.o(i.prvBytesInBuffer), (18 bytes).
    Removing stream_buffer.o(i.prvInitialiseNewStreamBuffer), (42 bytes).
    Removing stream_buffer.o(i.prvReadBytesFromBuffer), (64 bytes).
    Removing stream_buffer.o(i.prvReadMessageFromBuffer), (72 bytes).
    Removing stream_buffer.o(i.prvWriteBytesToBuffer), (64 bytes).
    Removing stream_buffer.o(i.prvWriteMessageToBuffer), (70 bytes).
    Removing stream_buffer.o(i.uxStreamBufferGetStreamBufferNotificationIndex), (4 bytes).
    Removing stream_buffer.o(i.vStreamBufferDelete), (16 bytes).
    Removing stream_buffer.o(i.vStreamBufferSetStreamBufferNotificationIndex), (4 bytes).
    Removing stream_buffer.o(i.xStreamBufferBytesAvailable), (4 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreate), (88 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsEmpty), (16 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsFull), (30 bytes).
    Removing stream_buffer.o(i.xStreamBufferNextMessageLengthBytes), (44 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceive), (180 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR), (60 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveFromISR), (110 bytes).
    Removing stream_buffer.o(i.xStreamBufferReset), (50 bytes).
    Removing stream_buffer.o(i.xStreamBufferResetFromISR), (62 bytes).
    Removing stream_buffer.o(i.xStreamBufferSend), (200 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendCompletedFromISR), (60 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendFromISR), (116 bytes).
    Removing stream_buffer.o(i.xStreamBufferSetTriggerLevel), (22 bytes).
    Removing stream_buffer.o(i.xStreamBufferSpacesAvailable), (32 bytes).
    Removing tasks.o(i.pcTaskGetName), (16 bytes).
    Removing tasks.o(i.prvTaskIsTaskSuspended), (52 bytes).
    Removing tasks.o(i.ulTaskGenericNotifyTake), (156 bytes).
    Removing tasks.o(i.ulTaskGenericNotifyValueClear), (48 bytes).
    Removing tasks.o(i.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(i.uxTaskPriorityGet), (28 bytes).
    Removing tasks.o(i.uxTaskPriorityGetFromISR), (40 bytes).
    Removing tasks.o(i.uxTaskResetEventItemValue), (24 bytes).
    Removing tasks.o(i.vTaskDelete), (172 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (68 bytes).
    Removing tasks.o(i.vTaskGenericNotifyGiveFromISR), (236 bytes).
    Removing tasks.o(i.vTaskPlaceOnUnorderedEventList), (64 bytes).
    Removing tasks.o(i.vTaskPrioritySet), (208 bytes).
    Removing tasks.o(i.vTaskRemoveFromUnorderedEventList), (168 bytes).
    Removing tasks.o(i.vTaskResetState), (48 bytes).
    Removing tasks.o(i.vTaskResume), (148 bytes).
    Removing tasks.o(i.vTaskSetTimeOutState), (32 bytes).
    Removing tasks.o(i.vTaskSuspend), (176 bytes).
    Removing tasks.o(i.xTaskCatchUpTicks), (36 bytes).
    Removing tasks.o(i.xTaskGenericNotify), (248 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (340 bytes).
    Removing tasks.o(i.xTaskGenericNotifyStateClear), (56 bytes).
    Removing tasks.o(i.xTaskGenericNotifyWait), (188 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandle), (12 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandleForCore), (20 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (12 bytes).
    Removing tasks.o(i.xTaskResumeFromISR), (172 bytes).
    Removing timers.o(i.pcTimerGetName), (4 bytes).
    Removing timers.o(i.prvInitialiseNewTimer), (56 bytes).
    Removing timers.o(i.pvTimerGetTimerID), (18 bytes).
    Removing timers.o(i.uxTimerGetReloadMode), (4 bytes).
    Removing timers.o(i.vTimerResetState), (16 bytes).
    Removing timers.o(i.vTimerSetReloadMode), (42 bytes).
    Removing timers.o(i.vTimerSetTimerID), (20 bytes).
    Removing timers.o(i.xTimerCreate), (52 bytes).
    Removing timers.o(i.xTimerGenericCommandFromISR), (44 bytes).
    Removing timers.o(i.xTimerGenericCommandFromTask), (68 bytes).
    Removing timers.o(i.xTimerGetExpiryTime), (4 bytes).
    Removing timers.o(i.xTimerGetPeriod), (4 bytes).
    Removing timers.o(i.xTimerGetReloadMode), (30 bytes).
    Removing timers.o(i.xTimerGetTimerDaemonTaskHandle), (12 bytes).
    Removing timers.o(i.xTimerIsTimerActive), (30 bytes).
    Removing heap_4.o(i.pvPortCalloc), (42 bytes).
    Removing heap_4.o(i.vPortGetHeapStats), (112 bytes).
    Removing heap_4.o(i.vPortHeapResetState), (20 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(i.vPortEndScheduler), (2 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing gpio_key.o(.rev16_text), (4 bytes).
    Removing gpio_key.o(.revsh_text), (4 bytes).
    Removing gpio_key.o(.rrx_text), (6 bytes).
    Removing adc_key.o(.rev16_text), (4 bytes).
    Removing adc_key.o(.revsh_text), (4 bytes).
    Removing adc_key.o(.rrx_text), (6 bytes).
    Removing device.o(i.device_get_binding), (60 bytes).
    Removing gpio_led.o(.rev16_text), (4 bytes).
    Removing gpio_led.o(.revsh_text), (4 bytes).
    Removing gpio_led.o(.rrx_text), (6 bytes).
    Removing gpio_led.o(i.gpio_led1_on), (12 bytes).
    Removing board_config.o(.rev16_text), (4 bytes).
    Removing board_config.o(.revsh_text), (4 bytes).
    Removing board_config.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing gpio.o(i.gpio_read_pin), (20 bytes).
    Removing gpio.o(i.gpio_toggle_pin), (20 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.i2c_ack), (64 bytes).
    Removing i2c.o(i.i2c_delay), (6 bytes).
    Removing i2c.o(i.i2c_init), (44 bytes).
    Removing i2c.o(i.i2c_nack), (56 bytes).
    Removing i2c.o(i.i2c_read_byte), (100 bytes).
    Removing i2c.o(i.i2c_send_byte), (80 bytes).
    Removing i2c.o(i.i2c_start), (60 bytes).
    Removing i2c.o(i.i2c_stop), (52 bytes).
    Removing i2c.o(i.i2c_wait_ack), (96 bytes).
    Removing i2c.o(.data), (8 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.spi_receive_data), (44 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing uart.o(.rev16_text), (4 bytes).
    Removing uart.o(.revsh_text), (4 bytes).
    Removing uart.o(.rrx_text), (6 bytes).
    Removing wdg.o(.rev16_text), (4 bytes).
    Removing wdg.o(.revsh_text), (4 bytes).
    Removing wdg.o(.rrx_text), (6 bytes).
    Removing wdg.o(i.iwdg_init), (24 bytes).
    Removing cjson.o(i.add_item_to_array), (52 bytes).
    Removing cjson.o(i.add_item_to_object), (100 bytes).
    Removing cjson.o(i.buffer_skip_whitespace), (54 bytes).
    Removing cjson.o(i.cJSON_AddArrayToObject), (48 bytes).
    Removing cjson.o(i.cJSON_AddBoolToObject), (52 bytes).
    Removing cjson.o(i.cJSON_AddFalseToObject), (48 bytes).
    Removing cjson.o(i.cJSON_AddItemReferenceToArray), (36 bytes).
    Removing cjson.o(i.cJSON_AddItemReferenceToObject), (48 bytes).
    Removing cjson.o(i.cJSON_AddItemToArray), (4 bytes).
    Removing cjson.o(i.cJSON_AddItemToObject), (20 bytes).
    Removing cjson.o(i.cJSON_AddItemToObjectCS), (20 bytes).
    Removing cjson.o(i.cJSON_AddNullToObject), (48 bytes).
    Removing cjson.o(i.cJSON_AddNumberToObject), (48 bytes).
    Removing cjson.o(i.cJSON_AddObjectToObject), (48 bytes).
    Removing cjson.o(i.cJSON_AddRawToObject), (52 bytes).
    Removing cjson.o(i.cJSON_AddStringToObject), (52 bytes).
    Removing cjson.o(i.cJSON_AddTrueToObject), (48 bytes).
    Removing cjson.o(i.cJSON_Compare), (308 bytes).
    Removing cjson.o(i.cJSON_CreateArray), (24 bytes).
    Removing cjson.o(i.cJSON_CreateArrayReference), (36 bytes).
    Removing cjson.o(i.cJSON_CreateBool), (32 bytes).
    Removing cjson.o(i.cJSON_CreateDoubleArray), (102 bytes).
    Removing cjson.o(i.cJSON_CreateFalse), (24 bytes).
    Removing cjson.o(i.cJSON_CreateFloatArray), (106 bytes).
    Removing cjson.o(i.cJSON_CreateIntArray), (106 bytes).
    Removing cjson.o(i.cJSON_CreateNull), (24 bytes).
    Removing cjson.o(i.cJSON_CreateNumber), (124 bytes).
    Removing cjson.o(i.cJSON_CreateObject), (24 bytes).
    Removing cjson.o(i.cJSON_CreateObjectReference), (36 bytes).
    Removing cjson.o(i.cJSON_CreateRaw), (48 bytes).
    Removing cjson.o(i.cJSON_CreateString), (48 bytes).
    Removing cjson.o(i.cJSON_CreateStringArray), (98 bytes).
    Removing cjson.o(i.cJSON_CreateStringReference), (36 bytes).
    Removing cjson.o(i.cJSON_CreateTrue), (24 bytes).
    Removing cjson.o(i.cJSON_Delete), (80 bytes).
    Removing cjson.o(i.cJSON_DeleteItemFromArray), (14 bytes).
    Removing cjson.o(i.cJSON_DeleteItemFromObject), (14 bytes).
    Removing cjson.o(i.cJSON_DeleteItemFromObjectCaseSensitive), (14 bytes).
    Removing cjson.o(i.cJSON_DetachItemFromArray), (30 bytes).
    Removing cjson.o(i.cJSON_DetachItemFromObject), (22 bytes).
    Removing cjson.o(i.cJSON_DetachItemFromObjectCaseSensitive), (22 bytes).
    Removing cjson.o(i.cJSON_DetachItemViaPointer), (70 bytes).
    Removing cjson.o(i.cJSON_Duplicate), (8 bytes).
    Removing cjson.o(i.cJSON_Duplicate_rec), (176 bytes).
    Removing cjson.o(i.cJSON_GetArrayItem), (12 bytes).
    Removing cjson.o(i.cJSON_GetArraySize), (26 bytes).
    Removing cjson.o(i.cJSON_GetErrorPtr), (16 bytes).
    Removing cjson.o(i.cJSON_GetNumberValue), (32 bytes).
    Removing cjson.o(i.cJSON_GetObjectItem), (6 bytes).
    Removing cjson.o(i.cJSON_GetObjectItemCaseSensitive), (6 bytes).
    Removing cjson.o(i.cJSON_GetStringValue), (20 bytes).
    Removing cjson.o(i.cJSON_HasObjectItem), (16 bytes).
    Removing cjson.o(i.cJSON_InitHooks), (76 bytes).
    Removing cjson.o(i.cJSON_InsertItemInArray), (76 bytes).
    Removing cjson.o(i.cJSON_IsArray), (22 bytes).
    Removing cjson.o(i.cJSON_IsBool), (22 bytes).
    Removing cjson.o(i.cJSON_IsFalse), (22 bytes).
    Removing cjson.o(i.cJSON_IsInvalid), (24 bytes).
    Removing cjson.o(i.cJSON_IsNull), (22 bytes).
    Removing cjson.o(i.cJSON_IsNumber), (22 bytes).
    Removing cjson.o(i.cJSON_IsObject), (22 bytes).
    Removing cjson.o(i.cJSON_IsRaw), (22 bytes).
    Removing cjson.o(i.cJSON_IsString), (22 bytes).
    Removing cjson.o(i.cJSON_IsTrue), (22 bytes).
    Removing cjson.o(i.cJSON_Minify), (118 bytes).
    Removing cjson.o(i.cJSON_New_Item), (24 bytes).
    Removing cjson.o(i.cJSON_Parse), (8 bytes).
    Removing cjson.o(i.cJSON_ParseWithLength), (8 bytes).
    Removing cjson.o(i.cJSON_ParseWithLengthOpts), (212 bytes).
    Removing cjson.o(i.cJSON_ParseWithOpts), (38 bytes).
    Removing cjson.o(i.cJSON_Print), (12 bytes).
    Removing cjson.o(i.cJSON_PrintBuffered), (100 bytes).
    Removing cjson.o(i.cJSON_PrintPreallocated), (76 bytes).
    Removing cjson.o(i.cJSON_PrintUnformatted), (12 bytes).
    Removing cjson.o(i.cJSON_ReplaceItemInArray), (34 bytes).
    Removing cjson.o(i.cJSON_ReplaceItemInObject), (6 bytes).
    Removing cjson.o(i.cJSON_ReplaceItemInObjectCaseSensitive), (6 bytes).
    Removing cjson.o(i.cJSON_ReplaceItemViaPointer), (88 bytes).
    Removing cjson.o(i.cJSON_SetNumberHelper), (112 bytes).
    Removing cjson.o(i.cJSON_SetValuestring), (116 bytes).
    Removing cjson.o(i.cJSON_Version), (40 bytes).
    Removing cjson.o(i.cJSON_free), (12 bytes).
    Removing cjson.o(i.cJSON_malloc), (12 bytes).
    Removing cjson.o(i.cJSON_strdup), (50 bytes).
    Removing cjson.o(i.case_insensitive_strcmp), (72 bytes).
    Removing cjson.o(i.cast_away_const), (2 bytes).
    Removing cjson.o(i.compare_double), (200 bytes).
    Removing cjson.o(i.create_reference), (56 bytes).
    Removing cjson.o(i.ensure), (172 bytes).
    Removing cjson.o(i.get_array_item), (26 bytes).
    Removing cjson.o(i.get_decimal_point), (4 bytes).
    Removing cjson.o(i.get_object_item), (70 bytes).
    Removing cjson.o(i.minify_string), (104 bytes).
    Removing cjson.o(i.parse_array), (218 bytes).
    Removing cjson.o(i.parse_hex4), (68 bytes).
    Removing cjson.o(i.parse_number), (340 bytes).
    Removing cjson.o(i.parse_object), (278 bytes).
    Removing cjson.o(i.parse_string), (330 bytes).
    Removing cjson.o(i.parse_value), (268 bytes).
    Removing cjson.o(i.print), (160 bytes).
    Removing cjson.o(i.print_array), (160 bytes).
    Removing cjson.o(i.print_number), (316 bytes).
    Removing cjson.o(i.print_object), (340 bytes).
    Removing cjson.o(i.print_string), (6 bytes).
    Removing cjson.o(i.print_string_ptr), (316 bytes).
    Removing cjson.o(i.print_value), (244 bytes).
    Removing cjson.o(i.replace_item_in_object), (92 bytes).
    Removing cjson.o(i.skip_multiline_comment), (40 bytes).
    Removing cjson.o(i.skip_oneline_comment), (34 bytes).
    Removing cjson.o(i.skip_utf8_bom), (52 bytes).
    Removing cjson.o(i.suffix_object), (6 bytes).
    Removing cjson.o(i.update_offset), (30 bytes).
    Removing cjson.o(i.utf16_literal_to_utf8), (214 bytes).
    Removing cjson.o(.bss), (15 bytes).
    Removing cjson.o(.data), (20 bytes).
    Removing driver_oled.o(.rev16_text), (4 bytes).
    Removing driver_oled.o(.revsh_text), (4 bytes).
    Removing driver_oled.o(.rrx_text), (6 bytes).
    Removing driver_oled.o(i.OLED_DrawArc), (594 bytes).
    Removing driver_oled.o(i.OLED_DrawCircle), (334 bytes).
    Removing driver_oled.o(i.OLED_DrawEllipse), (768 bytes).
    Removing driver_oled.o(i.OLED_DrawLine), (332 bytes).
    Removing driver_oled.o(i.OLED_DrawPoint), (52 bytes).
    Removing driver_oled.o(i.OLED_DrawRectangle), (124 bytes).
    Removing driver_oled.o(i.OLED_DrawTriangle), (196 bytes).
    Removing driver_oled.o(i.OLED_GetPoint), (56 bytes).
    Removing driver_oled.o(i.OLED_IsInAngle), (128 bytes).
    Removing driver_oled.o(i.OLED_Pow), (16 bytes).
    Removing driver_oled.o(i.OLED_Reverse), (40 bytes).
    Removing driver_oled.o(i.OLED_ReverseArea), (92 bytes).
    Removing driver_oled.o(i.OLED_ShowBinNum), (66 bytes).
    Removing driver_oled.o(i.OLED_ShowFloatNum), (232 bytes).
    Removing driver_oled.o(i.OLED_ShowHexNum), (92 bytes).
    Removing driver_oled.o(i.OLED_ShowNum), (72 bytes).
    Removing driver_oled.o(i.OLED_ShowSignedNum), (104 bytes).
    Removing driver_oled.o(i.OLED_UpdateArea), (104 bytes).
    Removing driver_oled.o(i.OLED_pnpoly), (112 bytes).
    Removing driver_oled_data.o(.constdata), (32 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing dfixi.o(.text), (62 bytes).
    Removing dfixui.o(.text), (50 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing drnd.o(.text), (136 bytes).
    Removing dfltul.o(.text), (24 bytes).

773 unused section(s) (total 59329 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/ctype/tolower.c         0x00000000   Number         0  tolower.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  drnd.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ../mathlib/strtod.c                      0x00000000   Number         0  strtod.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\CMSIS\Device\ST\STM32F4xx\Source\Templates\startup_stm32f407xx.s 0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_iwdg.c 0x00000000   Number         0  stm32f4xx_hal_iwdg.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\User\main.c 0x00000000   Number         0  main.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\User\stm32f4xx_it.c 0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Driver-Core\Driver-ST\Driver-F4\User\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_iwdg.c 0x00000000   Number         0  stm32f4xx_hal_iwdg.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\User\\main.c 0x00000000   Number         0  main.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\User\\stm32f4xx_it.c 0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\\Driver-Core\\Driver-ST\\Driver-F4\\User\\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\\sdk\\config\\board_config.c          0x00000000   Number         0  board_config.o ABSOLUTE
    ..\\sdk\\drivers\\core\\ST\\F4\\adc.c    0x00000000   Number         0  adc.o ABSOLUTE
    ..\\sdk\\drivers\\core\\ST\\F4\\gpio.c   0x00000000   Number         0  gpio.o ABSOLUTE
    ..\\sdk\\drivers\\core\\ST\\F4\\i2c.c    0x00000000   Number         0  i2c.o ABSOLUTE
    ..\\sdk\\drivers\\core\\ST\\F4\\spi.c    0x00000000   Number         0  spi.o ABSOLUTE
    ..\\sdk\\drivers\\core\\ST\\F4\\tim.c    0x00000000   Number         0  tim.o ABSOLUTE
    ..\\sdk\\drivers\\core\\ST\\F4\\uart.c   0x00000000   Number         0  uart.o ABSOLUTE
    ..\\sdk\\drivers\\core\\ST\\F4\\wdg.c    0x00000000   Number         0  wdg.o ABSOLUTE
    ..\\sdk\\drivers\\devices\\display\\oled\\driver_oled.c 0x00000000   Number         0  driver_oled.o ABSOLUTE
    ..\\sdk\\drivers\\devices\\input\\key\\adc_key\\adc_key.c 0x00000000   Number         0  adc_key.o ABSOLUTE
    ..\\sdk\\drivers\\devices\\input\\key\\gpio_key\\gpio_key.c 0x00000000   Number         0  gpio_key.o ABSOLUTE
    ..\\sdk\\drivers\\devices\\input\\key\\key.c 0x00000000   Number         0  key.o ABSOLUTE
    ..\\sdk\\drivers\\devices\\led\\gpio_led\\gpio_led.c 0x00000000   Number         0  gpio_led.o ABSOLUTE
    ..\\sdk\\middleware\\rtos\\freertos\\src\\port.c 0x00000000   Number         0  port.o ABSOLUTE
    ..\\sdk\\sdk_main.c                      0x00000000   Number         0  sdk_main.o ABSOLUTE
    ..\\sdk\\system\\debug\\debug.c          0x00000000   Number         0  debug.o ABSOLUTE
    ..\\sdk\\system\\delay\\delay.c          0x00000000   Number         0  delay.o ABSOLUTE
    ..\\sdk\\system\\event\\app_event.c      0x00000000   Number         0  app_event.o ABSOLUTE
    ..\\sdk\\system\\event\\system_event.c   0x00000000   Number         0  system_event.o ABSOLUTE
    ..\\sdk\\system\\sys\\sys.c              0x00000000   Number         0  sys.o ABSOLUTE
    ..\sdk\config\board_config.c             0x00000000   Number         0  board_config.o ABSOLUTE
    ..\sdk\drivers\core\ST\F4\adc.c          0x00000000   Number         0  adc.o ABSOLUTE
    ..\sdk\drivers\core\ST\F4\gpio.c         0x00000000   Number         0  gpio.o ABSOLUTE
    ..\sdk\drivers\core\ST\F4\i2c.c          0x00000000   Number         0  i2c.o ABSOLUTE
    ..\sdk\drivers\core\ST\F4\spi.c          0x00000000   Number         0  spi.o ABSOLUTE
    ..\sdk\drivers\core\ST\F4\tim.c          0x00000000   Number         0  tim.o ABSOLUTE
    ..\sdk\drivers\core\ST\F4\uart.c         0x00000000   Number         0  uart.o ABSOLUTE
    ..\sdk\drivers\core\ST\F4\wdg.c          0x00000000   Number         0  wdg.o ABSOLUTE
    ..\sdk\drivers\device.c                  0x00000000   Number         0  device.o ABSOLUTE
    ..\sdk\drivers\devices\display\oled\driver_oled.c 0x00000000   Number         0  driver_oled.o ABSOLUTE
    ..\sdk\drivers\devices\display\oled\driver_oled_data.c 0x00000000   Number         0  driver_oled_data.o ABSOLUTE
    ..\sdk\drivers\devices\input\key\adc_key\adc_key.c 0x00000000   Number         0  adc_key.o ABSOLUTE
    ..\sdk\drivers\devices\input\key\gpio_key\gpio_key.c 0x00000000   Number         0  gpio_key.o ABSOLUTE
    ..\sdk\drivers\devices\input\key\key.c   0x00000000   Number         0  key.o ABSOLUTE
    ..\sdk\drivers\devices\led\gpio_led\gpio_led.c 0x00000000   Number         0  gpio_led.o ABSOLUTE
    ..\sdk\middleware\cjson\cJSON.c          0x00000000   Number         0  cjson.o ABSOLUTE
    ..\sdk\middleware\rtos\freertos\src\croutine.c 0x00000000   Number         0  croutine.o ABSOLUTE
    ..\sdk\middleware\rtos\freertos\src\event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    ..\sdk\middleware\rtos\freertos\src\heap_4.c 0x00000000   Number         0  heap_4.o ABSOLUTE
    ..\sdk\middleware\rtos\freertos\src\list.c 0x00000000   Number         0  list.o ABSOLUTE
    ..\sdk\middleware\rtos\freertos\src\port.c 0x00000000   Number         0  port.o ABSOLUTE
    ..\sdk\middleware\rtos\freertos\src\queue.c 0x00000000   Number         0  queue.o ABSOLUTE
    ..\sdk\middleware\rtos\freertos\src\stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    ..\sdk\middleware\rtos\freertos\src\tasks.c 0x00000000   Number         0  tasks.o ABSOLUTE
    ..\sdk\middleware\rtos\freertos\src\timers.c 0x00000000   Number         0  timers.o ABSOLUTE
    ..\sdk\sdk_main.c                        0x00000000   Number         0  sdk_main.o ABSOLUTE
    ..\sdk\system\debug\debug.c              0x00000000   Number         0  debug.o ABSOLUTE
    ..\sdk\system\delay\delay.c              0x00000000   Number         0  delay.o ABSOLUTE
    ..\sdk\system\event\app_event.c          0x00000000   Number         0  app_event.o ABSOLUTE
    ..\sdk\system\event\system_event.c       0x00000000   Number         0  system_event.o ABSOLUTE
    ..\sdk\system\sys\sys.c                  0x00000000   Number         0  sys.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x080001a0   Section      190  port.o(.emb_text)
    $v0                                      0x080001a0   Number         0  port.o(.emb_text)
    .text                                    0x08000260   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x08000260   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000284   Section        0  uldiv.o(.text)
    .text                                    0x080002e6   Section        0  memcpya.o(.text)
    .text                                    0x0800030a   Section        0  memseta.o(.text)
    .text                                    0x0800032e   Section        0  strlen.o(.text)
    .text                                    0x0800033c   Section        0  strcmp.o(.text)
    .text                                    0x08000358   Section        0  dadd.o(.text)
    .text                                    0x080004a6   Section        0  dmul.o(.text)
    .text                                    0x0800058a   Section        0  ddiv.o(.text)
    .text                                    0x08000668   Section        0  dfltui.o(.text)
    .text                                    0x08000682   Section        0  f2d.o(.text)
    .text                                    0x080006a8   Section       48  cdrcmple.o(.text)
    .text                                    0x080006d8   Section        0  d2f.o(.text)
    .text                                    0x08000710   Section        0  uidiv.o(.text)
    .text                                    0x0800073c   Section        0  llshl.o(.text)
    .text                                    0x0800075a   Section        0  llushr.o(.text)
    .text                                    0x0800077a   Section        0  llsshr.o(.text)
    .text                                    0x0800079e   Section        0  fepilogue.o(.text)
    .text                                    0x0800079e   Section        0  iusefp.o(.text)
    .text                                    0x0800080c   Section        0  depilogue.o(.text)
    .text                                    0x080008c6   Section        0  dfixul.o(.text)
    .text                                    0x080008f8   Section       36  init.o(.text)
    i.ADC_Init                               0x0800091c   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x0800091d   Thumb Code   298  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.BusFault_Handler                       0x08000a4c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000a4e   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.HAL_ADC_ConfigChannel                  0x08000a50   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_GetValue                       0x08000ba8   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue)
    i.HAL_ADC_Init                           0x08000bae   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08000c02   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_PollForConversion              0x08000c04   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion)
    i.HAL_ADC_Start                          0x08000cb0   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start)
    i.HAL_ADC_Stop                           0x08000dc4   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop)
    i.HAL_GPIO_Init                          0x08000e00   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x08000fd0   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetREVID                           0x08000fe0   Section        0  stm32f4xx_hal.o(i.HAL_GetREVID)
    i.HAL_GetTick                            0x08000fec   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IWDG_Refresh                       0x08000ff8   Section        0  stm32f4xx_hal_iwdg.o(i.HAL_IWDG_Refresh)
    i.HAL_IncTick                            0x08001004   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001014   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001040   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001084   Section        0  stm32f4xx_hal.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08001086   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x080010a0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x080010e0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08001104   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08001250   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x0800125c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x0800127c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x0800129c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001304   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SPI_Init                           0x080016c4   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x08001782   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_MspInit)
    i.HAL_SPI_Transmit                       0x08001784   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit)
    i.HAL_SYSTICK_Config                     0x08001918   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIM_PWM_ConfigChannel              0x08001940   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08001a18   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08001a62   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_Start                      0x08001a64   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_UART_Init                          0x08001b40   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08001ba6   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Transmit                      0x08001ba8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HardFault_Handler                      0x08001c56   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x08001c58   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001c5a   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08001c5c   Section        0  driver_oled.o(i.OLED_Clear)
    i.OLED_ClearArea                         0x08001c84   Section        0  driver_oled.o(i.OLED_ClearArea)
    i.OLED_GPIO_Init                         0x08001ce0   Section        0  driver_oled.o(i.OLED_GPIO_Init)
    i.OLED_Init                              0x08001d42   Section        0  driver_oled.o(i.OLED_Init)
    i.OLED_Printf                            0x08001dde   Section        0  driver_oled.o(i.OLED_Printf)
    i.OLED_SetCursor                         0x08001e08   Section        0  driver_oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08001e2c   Section        0  driver_oled.o(i.OLED_ShowChar)
    i.OLED_ShowImage                         0x08001e68   Section        0  driver_oled.o(i.OLED_ShowImage)
    i.OLED_ShowString                        0x08001f1c   Section        0  driver_oled.o(i.OLED_ShowString)
    i.OLED_Update                            0x080020a4   Section        0  driver_oled.o(i.OLED_Update)
    i.OLED_W_CS                              0x080020cc   Section        0  driver_oled.o(i.OLED_W_CS)
    i.OLED_W_DC                              0x080020d4   Section        0  driver_oled.o(i.OLED_W_DC)
    i.OLED_W_RES                             0x080020dc   Section        0  driver_oled.o(i.OLED_W_RES)
    i.OLED_WriteCommand                      0x080020e4   Section        0  driver_oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08002108   Section        0  driver_oled.o(i.OLED_WriteData)
    i.PendSV_Handler                         0x08002132   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SPI_EndRxTxTransaction                 0x08002138   Section        0  stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    SPI_EndRxTxTransaction                   0x08002139   Thumb Code   114  stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    i.SPI_WaitFlagStateUntilTimeout          0x080021b4   Section        0  stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    SPI_WaitFlagStateUntilTimeout            0x080021b5   Thumb Code   184  stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    i.SVC_Handler                            0x08002270   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08002274   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x0800228c   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x0800229c   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08002378   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_OC1_SetConfig                      0x08002394   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08002395   Thumb Code    96  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x080023fc   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08002470   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08002471   Thumb Code   104  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x080024e0   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x080024e1   Thumb Code    74  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.UART_EndRxTransfer                     0x08002534   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08002535   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_SetConfig                         0x08002584   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08002585   Thumb Code   266  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_WaitOnFlagUntilTimeout            0x08002698   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08002699   Thumb Code   116  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x0800270c   Section        0  uart.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08002728   Section        0  uart.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08002748   Section        0  uart.o(i.USART3_IRQHandler)
    i.UsageFault_Handler                     0x08002768   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0snprintf                            0x0800276c   Section        0  printfa.o(i.__0snprintf)
    i.__0vsnprintf                           0x080027a0   Section        0  printfa.o(i.__0vsnprintf)
    i.__0vsprintf                            0x080027d4   Section        0  printfa.o(i.__0vsprintf)
    i.__NVIC_SetPriority                     0x080027f8   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080027f9   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x08002818   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08002826   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08002828   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x08002838   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08002839   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x080029bc   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080029bd   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08003070   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08003071   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08003094   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08003095   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x080030c2   Section        0  printfa.o(i._snputc)
    _snputc                                  0x080030c3   Thumb Code    22  printfa.o(i._snputc)
    i._sputc                                 0x080030d8   Section        0  printfa.o(i._sputc)
    _sputc                                   0x080030d9   Thumb Code    10  printfa.o(i._sputc)
    i._sys_device_do_config_level            0x080030e4   Section        0  device.o(i._sys_device_do_config_level)
    i.adc_get_value                          0x0800310c   Section        0  adc.o(i.adc_get_value)
    i.adc_pin_init                           0x0800313c   Section        0  adc.o(i.adc_pin_init)
    i.adc_read_task                          0x080032a8   Section        0  adc.o(i.adc_read_task)
    adc_read_task                            0x080032a9   Thumb Code    68  adc.o(i.adc_read_task)
    i.app_event_init                         0x080032f4   Section        0  app_event.o(i.app_event_init)
    i.app_init                               0x08003338   Section        0  sdk_main.o(i.app_init)
    i.app_msg_get_notify                     0x0800333c   Section        0  app_event.o(i.app_msg_get_notify)
    i.app_msg_handle                         0x08003358   Section        0  app_event.o(i.app_msg_handle)
    app_msg_handle                           0x08003359   Thumb Code    86  app_event.o(i.app_msg_handle)
    i.app_process_msg                        0x080033fc   Section        0  app_event.o(i.app_process_msg)
    app_process_msg                          0x080033fd   Thumb Code    10  app_event.o(i.app_process_msg)
    i.board_init                             0x08003406   Section        0  sdk_main.o(i.board_init)
    i.debug_error                            0x08003420   Section        0  debug.o(i.debug_error)
    i.debug_init                             0x08003488   Section        0  debug.o(i.debug_init)
    i.debug_message                          0x080034b0   Section        0  debug.o(i.debug_message)
    i.debug_printf                           0x08003518   Section        0  debug.o(i.debug_printf)
    i.debug_recv_callback                    0x0800356c   Section        0  debug.o(i.debug_recv_callback)
    debug_recv_callback                      0x0800356d   Thumb Code    66  debug.o(i.debug_recv_callback)
    i.debug_send_data                        0x080035c0   Section        0  debug.o(i.debug_send_data)
    i.debug_warning                          0x080035cc   Section        0  debug.o(i.debug_warning)
    i.delay_init                             0x08003638   Section        0  delay.o(i.delay_init)
    i.display_show_init                      0x08003644   Section        0  sdk_main.o(i.display_show_init)
    i.get_debug_time                         0x08003648   Section        0  debug.o(i.get_debug_time)
    i.get_temperature_mcu                    0x080036ac   Section        0  sdk_main.o(i.get_temperature_mcu)
    i.gpio_led_blue                          0x080036f8   Section        0  gpio_led.o(i.gpio_led_blue)
    i.gpio_led_green                         0x08003704   Section        0  gpio_led.o(i.gpio_led_green)
    i.gpio_led_init                          0x08003710   Section        0  gpio_led.o(i.gpio_led_init)
    i.gpio_led_off                           0x0800373c   Section        0  gpio_led.o(i.gpio_led_off)
    i.gpio_led_red                           0x0800375e   Section        0  gpio_led.o(i.gpio_led_red)
    i.gpio_led_run                           0x0800376a   Section        0  gpio_led.o(i.gpio_led_run)
    i.gpio_pin_init                          0x08003788   Section        0  gpio.o(i.gpio_pin_init)
    i.gpio_pin_init_alternate                0x0800391c   Section        0  gpio.o(i.gpio_pin_init_alternate)
    i.gpio_write_pin                         0x08003b5c   Section        0  gpio.o(i.gpio_write_pin)
    i.iwdg_feed                              0x08003b70   Section        0  wdg.o(i.iwdg_feed)
    i.iwdg_feed_task                         0x08003b7c   Section        0  sdk_main.o(i.iwdg_feed_task)
    i.key_init                               0x08003b98   Section        0  key.o(i.key_init)
    i.main                                   0x08003b9a   Section        0  main.o(i.main)
    i.process_key_event                      0x08003bb8   Section        0  key.o(i.process_key_event)
    i.process_sys_event                      0x08003bec   Section        0  system_event.o(i.process_sys_event)
    process_sys_event                        0x08003bed   Thumb Code    34  system_event.o(i.process_sys_event)
    i.prvAddCurrentTaskToDelayedList         0x08003c10   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x08003c11   Thumb Code   140  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvAddNewTaskToReadyList               0x08003ca4   Section        0  tasks.o(i.prvAddNewTaskToReadyList)
    prvAddNewTaskToReadyList                 0x08003ca5   Thumb Code   156  tasks.o(i.prvAddNewTaskToReadyList)
    i.prvCheckForValidListAndQueue           0x08003d4c   Section        0  timers.o(i.prvCheckForValidListAndQueue)
    prvCheckForValidListAndQueue             0x08003d4d   Thumb Code    54  timers.o(i.prvCheckForValidListAndQueue)
    i.prvCheckTasksWaitingTermination        0x08003d8c   Section        0  tasks.o(i.prvCheckTasksWaitingTermination)
    prvCheckTasksWaitingTermination          0x08003d8d   Thumb Code    52  tasks.o(i.prvCheckTasksWaitingTermination)
    i.prvCopyDataFromQueue                   0x08003dc8   Section        0  queue.o(i.prvCopyDataFromQueue)
    prvCopyDataFromQueue                     0x08003dc9   Thumb Code    40  queue.o(i.prvCopyDataFromQueue)
    i.prvCreateIdleTasks                     0x08003df0   Section        0  tasks.o(i.prvCreateIdleTasks)
    prvCreateIdleTasks                       0x08003df1   Thumb Code    62  tasks.o(i.prvCreateIdleTasks)
    i.prvCreateTask                          0x08003e40   Section        0  tasks.o(i.prvCreateTask)
    prvCreateTask                            0x08003e41   Thumb Code    92  tasks.o(i.prvCreateTask)
    i.prvDeleteTCB                           0x08003e9c   Section        0  tasks.o(i.prvDeleteTCB)
    prvDeleteTCB                             0x08003e9d   Thumb Code    20  tasks.o(i.prvDeleteTCB)
    i.prvGetNextExpireTime                   0x08003eb0   Section        0  timers.o(i.prvGetNextExpireTime)
    prvGetNextExpireTime                     0x08003eb1   Thumb Code    30  timers.o(i.prvGetNextExpireTime)
    i.prvHeapInit                            0x08003ed4   Section        0  heap_4.o(i.prvHeapInit)
    prvHeapInit                              0x08003ed5   Thumb Code    62  heap_4.o(i.prvHeapInit)
    i.prvIdleTask                            0x08003f1c   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x08003f1d   Thumb Code    30  tasks.o(i.prvIdleTask)
    i.prvInitialiseNewQueue                  0x08003f44   Section        0  queue.o(i.prvInitialiseNewQueue)
    prvInitialiseNewQueue                    0x08003f45   Thumb Code    24  queue.o(i.prvInitialiseNewQueue)
    i.prvInitialiseNewTask                   0x08003f5c   Section        0  tasks.o(i.prvInitialiseNewTask)
    prvInitialiseNewTask                     0x08003f5d   Thumb Code   110  tasks.o(i.prvInitialiseNewTask)
    i.prvInitialiseTaskLists                 0x08003fcc   Section        0  tasks.o(i.prvInitialiseTaskLists)
    prvInitialiseTaskLists                   0x08003fcd   Thumb Code    76  tasks.o(i.prvInitialiseTaskLists)
    i.prvInsertBlockIntoFreeList             0x08004024   Section        0  heap_4.o(i.prvInsertBlockIntoFreeList)
    prvInsertBlockIntoFreeList               0x08004025   Thumb Code    78  heap_4.o(i.prvInsertBlockIntoFreeList)
    i.prvInsertTimerInActiveList             0x08004078   Section        0  timers.o(i.prvInsertTimerInActiveList)
    prvInsertTimerInActiveList               0x08004079   Thumb Code    60  timers.o(i.prvInsertTimerInActiveList)
    i.prvIsQueueEmpty                        0x080040b8   Section        0  queue.o(i.prvIsQueueEmpty)
    prvIsQueueEmpty                          0x080040b9   Thumb Code    26  queue.o(i.prvIsQueueEmpty)
    i.prvProcessExpiredTimer                 0x080040d4   Section        0  timers.o(i.prvProcessExpiredTimer)
    prvProcessExpiredTimer                   0x080040d5   Thumb Code    58  timers.o(i.prvProcessExpiredTimer)
    i.prvProcessReceivedCommands             0x08004114   Section        0  timers.o(i.prvProcessReceivedCommands)
    prvProcessReceivedCommands               0x08004115   Thumb Code   212  timers.o(i.prvProcessReceivedCommands)
    i.prvProcessTimerOrBlockTask             0x080041ec   Section        0  timers.o(i.prvProcessTimerOrBlockTask)
    prvProcessTimerOrBlockTask               0x080041ed   Thumb Code   100  timers.o(i.prvProcessTimerOrBlockTask)
    i.prvReloadTimer                         0x08004258   Section        0  timers.o(i.prvReloadTimer)
    prvReloadTimer                           0x08004259   Thumb Code    40  timers.o(i.prvReloadTimer)
    i.prvResetNextTaskUnblockTime            0x08004280   Section        0  tasks.o(i.prvResetNextTaskUnblockTime)
    prvResetNextTaskUnblockTime              0x08004281   Thumb Code    28  tasks.o(i.prvResetNextTaskUnblockTime)
    i.prvSampleTimeNow                       0x080042a0   Section        0  timers.o(i.prvSampleTimeNow)
    prvSampleTimeNow                         0x080042a1   Thumb Code    38  timers.o(i.prvSampleTimeNow)
    i.prvSwitchTimerLists                    0x080042cc   Section        0  timers.o(i.prvSwitchTimerLists)
    prvSwitchTimerLists                      0x080042cd   Thumb Code    38  timers.o(i.prvSwitchTimerLists)
    i.prvTaskExitError                       0x080042f8   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x080042f9   Thumb Code    16  port.o(i.prvTaskExitError)
    i.prvTimerTask                           0x08004308   Section        0  timers.o(i.prvTimerTask)
    prvTimerTask                             0x08004309   Thumb Code    20  timers.o(i.prvTimerTask)
    i.prvUnlockQueue                         0x0800431c   Section        0  queue.o(i.prvUnlockQueue)
    prvUnlockQueue                           0x0800431d   Thumb Code   106  queue.o(i.prvUnlockQueue)
    i.pvPortMalloc                           0x08004388   Section        0  heap_4.o(i.pvPortMalloc)
    i.pxPortInitialiseStack                  0x08004438   Section        0  port.o(i.pxPortInitialiseStack)
    i.sdk_init                               0x0800445c   Section        0  sdk_main.o(i.sdk_init)
    i.sdk_main                               0x080044b0   Section        0  sdk_main.o(i.sdk_main)
    i.spi_pin_init                           0x080045c0   Section        0  spi.o(i.spi_pin_init)
    i.spi_send_data                          0x0800476c   Section        0  spi.o(i.spi_send_data)
    i.sys_check_rst                          0x08004798   Section        0  sys.o(i.sys_check_rst)
    i.sys_event_get_notify                   0x080048e0   Section        0  system_event.o(i.sys_event_get_notify)
    i.sys_event_init                         0x080048f4   Section        0  system_event.o(i.sys_event_init)
    i.sys_get_tick                           0x08004934   Section        0  sys.o(i.sys_get_tick)
    i.sys_stm32_clock_init                   0x08004938   Section        0  sys.o(i.sys_stm32_clock_init)
    i.system_init                            0x080049f0   Section        0  sdk_main.o(i.system_init)
    i.tim_pwm_pin_init                       0x08004a1c   Section        0  tim.o(i.tim_pwm_pin_init)
    i.tim_pwm_set_ccr                        0x08004c30   Section        0  tim.o(i.tim_pwm_set_ccr)
    i.uart_add_recv_callback                 0x08004ce0   Section        0  uart.o(i.uart_add_recv_callback)
    i.uart_init                              0x08004cec   Section        0  uart.o(i.uart_init)
    i.uart_send_data                         0x08004e20   Section        0  uart.o(i.uart_send_data)
    i.uxListRemove                           0x08004e48   Section        0  list.o(i.uxListRemove)
    i.vListInitialise                        0x08004e6c   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x08004e82   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x08004e88   Section        0  list.o(i.vListInsert)
    i.vPortEnterCritical                     0x08004eb8   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x08004ed4   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x08004eec   Section        0  heap_4.o(i.vPortFree)
    i.vPortSetupTimerInterrupt               0x08004f30   Section        0  port.o(i.vPortSetupTimerInterrupt)
    i.vQueueWaitForMessageRestricted         0x08004f48   Section        0  queue.o(i.vQueueWaitForMessageRestricted)
    i.vTaskDelay                             0x08004f90   Section        0  tasks.o(i.vTaskDelay)
    i.vTaskInternalSetTimeOutState           0x08004fc4   Section        0  tasks.o(i.vTaskInternalSetTimeOutState)
    i.vTaskMissedYield                       0x08004fd4   Section        0  tasks.o(i.vTaskMissedYield)
    i.vTaskPlaceOnEventList                  0x08004fe0   Section        0  tasks.o(i.vTaskPlaceOnEventList)
    i.vTaskPlaceOnEventListRestricted        0x08005000   Section        0  tasks.o(i.vTaskPlaceOnEventListRestricted)
    i.vTaskStartScheduler                    0x08005044   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x08005080   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x08005090   Section        0  tasks.o(i.vTaskSwitchContext)
    i.xPortStartScheduler                    0x080050e0   Section        0  port.o(i.xPortStartScheduler)
    i.xPortSysTickHandler                    0x08005120   Section        0  port.o(i.xPortSysTickHandler)
    i.xQueueGenericCreate                    0x0800514c   Section        0  queue.o(i.xQueueGenericCreate)
    i.xQueueGenericReset                     0x0800518c   Section        0  queue.o(i.xQueueGenericReset)
    i.xQueueReceive                          0x08005220   Section        0  queue.o(i.xQueueReceive)
    i.xTaskCheckForTimeOut                   0x08005310   Section        0  tasks.o(i.xTaskCheckForTimeOut)
    i.xTaskCreate                            0x08005368   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskDelayUntil                        0x08005388   Section        0  tasks.o(i.xTaskDelayUntil)
    i.xTaskGetSchedulerState                 0x080053e4   Section        0  tasks.o(i.xTaskGetSchedulerState)
    i.xTaskGetTickCount                      0x08005400   Section        0  tasks.o(i.xTaskGetTickCount)
    i.xTaskIncrementTick                     0x0800540c   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskRemoveFromEventList               0x0800552c   Section        0  tasks.o(i.xTaskRemoveFromEventList)
    i.xTaskResumeAll                         0x080055f8   Section        0  tasks.o(i.xTaskResumeAll)
    i.xTimerCreateTimerTask                  0x08005724   Section        0  timers.o(i.xTimerCreateTimerTask)
    .constdata                               0x08005760   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x08005770   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08005778   Section       14  sys.o(.constdata)
    __func__                                 0x08005778   Data          14  sys.o(.constdata)
    .constdata                               0x08005786   Section       31  debug.o(.constdata)
    __func__                                 0x08005786   Data          11  debug.o(.constdata)
    __func__                                 0x08005791   Data          20  debug.o(.constdata)
    .constdata                               0x080057a5   Section       15  app_event.o(.constdata)
    __func__                                 0x080057a5   Data          15  app_event.o(.constdata)
    .constdata                               0x080057b4   Section        9  sdk_main.o(.constdata)
    __func__                                 0x080057b4   Data           9  sdk_main.o(.constdata)
    .constdata                               0x080057bd   Section       18  key.o(.constdata)
    __func__                                 0x080057bd   Data          18  key.o(.constdata)
    .constdata                               0x080057d0   Section      239  adc.o(.constdata)
    adc_channel_map                          0x080057d0   Data         226  adc.o(.constdata)
    __func__                                 0x080058b2   Data          13  adc.o(.constdata)
    .constdata                               0x080058c0   Section      896  gpio.o(.constdata)
    .constdata                               0x08005c40   Section     1520  driver_oled_data.o(.constdata)
    .constdata                               0x08006230   Section      570  driver_oled_data.o(.constdata)
    .constdata                               0x0800646a   Section      259  driver_oled_data.o(.constdata)
    .conststrlit                             0x08006570   Section        9  gpio_led.o(.conststrlit)
    .devconfig.init                          0x0800657c   Section       12  gpio_led.o(.devconfig.init)
    __config_gpio_led                        0x0800657c   Data          12  gpio_led.o(.devconfig.init)
    __tagsym$$used                           0x0800657c   Number         0  gpio_led.o(.devconfig.init)
    .device_DRIVER                           0x20000100   Section       12  gpio_led.o(.device_DRIVER)
    __device_gpio_led                        0x20000100   Data          12  gpio_led.o(.device_DRIVER)
    __tagsym$$used                           0x20000100   Number         0  gpio_led.o(.device_DRIVER)
    .data                                    0x20000400   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000040c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000410   Section        4  delay.o(.data)
    g_fac_us                                 0x20000410   Data           4  delay.o(.data)
    .data                                    0x20000414   Section        2  debug.o(.data)
    debug_recv_len                           0x20000414   Data           2  debug.o(.data)
    .data                                    0x20000418   Section        8  app_event.o(.data)
    app_msg_queue                            0x20000418   Data           4  app_event.o(.data)
    app_msg_task                             0x2000041c   Data           4  app_event.o(.data)
    .data                                    0x20000420   Section        8  system_event.o(.data)
    sys_event_queue                          0x20000420   Data           4  system_event.o(.data)
    sys_event_task                           0x20000424   Data           4  system_event.o(.data)
    .data                                    0x20000428   Section       12  sdk_main.o(.data)
    aaa                                      0x20000428   Data           2  sdk_main.o(.data)
    tick                                     0x2000042c   Data           4  sdk_main.o(.data)
    tick                                     0x20000430   Data           4  sdk_main.o(.data)
    .data                                    0x20000434   Section       64  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x20000438   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x2000043c   Data           4  tasks.o(.data)
    xTickCount                               0x20000440   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x20000444   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x20000448   Data           4  tasks.o(.data)
    xPendedTicks                             0x2000044c   Data           4  tasks.o(.data)
    xYieldPendings                           0x20000450   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x20000454   Data           4  tasks.o(.data)
    uxTaskNumber                             0x20000458   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x2000045c   Data           4  tasks.o(.data)
    uxTopUsedPriority                        0x20000460   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x20000464   Data           4  tasks.o(.data)
    pxDelayedTaskList                        0x20000468   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x2000046c   Data           4  tasks.o(.data)
    xIdleTaskHandles                         0x20000470   Data           4  tasks.o(.data)
    .data                                    0x20000474   Section       20  timers.o(.data)
    xTimerQueue                              0x20000474   Data           4  timers.o(.data)
    xTimerTaskHandle                         0x20000478   Data           4  timers.o(.data)
    xLastTime                                0x2000047c   Data           4  timers.o(.data)
    pxCurrentTimerList                       0x20000480   Data           4  timers.o(.data)
    pxOverflowTimerList                      0x20000484   Data           4  timers.o(.data)
    .data                                    0x20000488   Section       28  heap_4.o(.data)
    pxEnd                                    0x20000488   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x2000048c   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x20000490   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulAllocations           0x20000494   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulFrees                 0x20000498   Data           4  heap_4.o(.data)
    xStart                                   0x2000049c   Data           8  heap_4.o(.data)
    .data                                    0x200004a4   Section        4  port.o(.data)
    uxCriticalNesting                        0x200004a4   Data           4  port.o(.data)
    .data                                    0x200004a8   Section       32  device.o(.data)
    config_levels_start                      0x200004a8   Data          16  device.o(.data)
    config_levels_end                        0x200004b8   Data          16  device.o(.data)
    .data                                    0x200004c8   Section        4  gpio_led.o(.data)
    .data                                    0x200004cc   Section       20  gpio_led.o(.data)
    .data                                    0x200004e0   Section        3  adc.o(.data)
    adc_regular_rank_cnt                     0x200004e0   Data           1  adc.o(.data)
    is_adc_task_start                        0x200004e1   Data           1  adc.o(.data)
    is_adc1_start                            0x200004e2   Data           1  adc.o(.data)
    .bss                                     0x200004e4   Section     1184  debug.o(.bss)
    log_str                                  0x200004e4   Data        1024  debug.o(.bss)
    debug_recv_buf                           0x20000904   Data         128  debug.o(.bss)
    .bss                                     0x20000984   Section      300  tasks.o(.bss)
    pxReadyTasksLists                        0x20000984   Data         200  tasks.o(.bss)
    xDelayedTaskList1                        0x20000a4c   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x20000a60   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x20000a74   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x20000a88   Data          20  tasks.o(.bss)
    xSuspendedTaskList                       0x20000a9c   Data          20  tasks.o(.bss)
    .bss                                     0x20000ab0   Section       40  timers.o(.bss)
    xActiveTimerList1                        0x20000ab0   Data          20  timers.o(.bss)
    xActiveTimerList2                        0x20000ac4   Data          20  timers.o(.bss)
    .bss                                     0x20000ad8   Section     5120  heap_4.o(.bss)
    ucHeap                                   0x20000ad8   Data        5120  heap_4.o(.bss)
    .bss                                     0x20001ed8   Section      264  adc.o(.bss)
    adc_pin_value_map                        0x20001ed8   Data          16  adc.o(.bss)
    adc_value                                0x20001ee8   Data          32  adc.o(.bss)
    adc1                                     0x20001f08   Data          72  adc.o(.bss)
    adc2                                     0x20001f50   Data          72  adc.o(.bss)
    adc3                                     0x20001f98   Data          72  adc.o(.bss)
    .bss                                     0x20001fe0   Section      264  spi.o(.bss)
    .bss                                     0x200020e8   Section      720  tim.o(.bss)
    .bss                                     0x200023b8   Section      232  uart.o(.bss)
    uart1                                    0x200023b8   Data          72  uart.o(.bss)
    uart2                                    0x20002400   Data          72  uart.o(.bss)
    uart3                                    0x20002448   Data          72  uart.o(.bss)
    .bss                                     0x200024a0   Section       12  wdg.o(.bss)
    .bss                                     0x200024ac   Section     1024  driver_oled.o(.bss)
    STACK                                    0x200028b0   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    _scanf_int                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    vPortSVCHandler                          0x080001a1   Thumb Code    28  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x080001c1   Thumb Code    36  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvEnableVFP  0x080001e9   Thumb Code    16  port.o(.emb_text)
    xPortPendSVHandler                       0x080001fd   Thumb Code    88  port.o(.emb_text)
    vPortGetIPSR                             0x08000259   Thumb Code     6  port.o(.emb_text)
    Reset_Handler                            0x08000261   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08000285   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x080002e7   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x080002e7   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x080002e7   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800030b   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800030b   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800030b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000319   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000319   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000319   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800031d   Thumb Code    18  memseta.o(.text)
    strlen                                   0x0800032f   Thumb Code    14  strlen.o(.text)
    strcmp                                   0x0800033d   Thumb Code    28  strcmp.o(.text)
    __aeabi_dadd                             0x08000359   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x0800049b   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080004a1   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080004a7   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x0800058b   Thumb Code   222  ddiv.o(.text)
    __aeabi_ui2d                             0x08000669   Thumb Code    26  dfltui.o(.text)
    __aeabi_f2d                              0x08000683   Thumb Code    38  f2d.o(.text)
    __aeabi_cdrcmple                         0x080006a9   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_d2f                              0x080006d9   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x08000711   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000711   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x0800073d   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800073d   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800075b   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800075b   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x0800077b   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x0800077b   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x0800079f   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x0800079f   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x080007b1   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x0800080d   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0800082b   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x080008c7   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x080008f9   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080008f9   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x08000a4d   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08000a4f   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    HAL_ADC_ConfigChannel                    0x08000a51   Thumb Code   326  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_GetValue                         0x08000ba9   Thumb Code     6  stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue)
    HAL_ADC_Init                             0x08000baf   Thumb Code    84  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08000c03   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_PollForConversion                0x08000c05   Thumb Code   170  stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion)
    HAL_ADC_Start                            0x08000cb1   Thumb Code   252  stm32f4xx_hal_adc.o(i.HAL_ADC_Start)
    HAL_ADC_Stop                             0x08000dc5   Thumb Code    60  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop)
    HAL_GPIO_Init                            0x08000e01   Thumb Code   420  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08000fd1   Thumb Code    14  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetREVID                             0x08000fe1   Thumb Code     8  stm32f4xx_hal.o(i.HAL_GetREVID)
    HAL_GetTick                              0x08000fed   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IWDG_Refresh                         0x08000ff9   Thumb Code    12  stm32f4xx_hal_iwdg.o(i.HAL_IWDG_Refresh)
    HAL_IncTick                              0x08001005   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001015   Thumb Code    40  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001041   Thumb Code    58  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001085   Thumb Code     2  stm32f4xx_hal.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001087   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080010a1   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080010e1   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001105   Thumb Code   310  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08001251   Thumb Code     6  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x0800125d   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x0800127d   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x0800129d   Thumb Code    94  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001305   Thumb Code   942  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SPI_Init                             0x080016c5   Thumb Code   190  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x08001783   Thumb Code     2  stm32f4xx_hal_spi.o(i.HAL_SPI_MspInit)
    HAL_SPI_Transmit                         0x08001785   Thumb Code   404  stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit)
    HAL_SYSTICK_Config                       0x08001919   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIM_PWM_ConfigChannel                0x08001941   Thumb Code   216  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08001a19   Thumb Code    74  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08001a63   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_Start                        0x08001a65   Thumb Code   190  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_UART_Init                            0x08001b41   Thumb Code   102  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08001ba7   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_MspInit)
    HAL_UART_Transmit                        0x08001ba9   Thumb Code   174  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HardFault_Handler                        0x08001c57   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x08001c59   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001c5b   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08001c5d   Thumb Code    34  driver_oled.o(i.OLED_Clear)
    OLED_ClearArea                           0x08001c85   Thumb Code    88  driver_oled.o(i.OLED_ClearArea)
    OLED_GPIO_Init                           0x08001ce1   Thumb Code    98  driver_oled.o(i.OLED_GPIO_Init)
    OLED_Init                                0x08001d43   Thumb Code   156  driver_oled.o(i.OLED_Init)
    OLED_Printf                              0x08001ddf   Thumb Code    42  driver_oled.o(i.OLED_Printf)
    OLED_SetCursor                           0x08001e09   Thumb Code    34  driver_oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08001e2d   Thumb Code    52  driver_oled.o(i.OLED_ShowChar)
    OLED_ShowImage                           0x08001e69   Thumb Code   176  driver_oled.o(i.OLED_ShowImage)
    OLED_ShowString                          0x08001f1d   Thumb Code   382  driver_oled.o(i.OLED_ShowString)
    OLED_Update                              0x080020a5   Thumb Code    34  driver_oled.o(i.OLED_Update)
    OLED_W_CS                                0x080020cd   Thumb Code     8  driver_oled.o(i.OLED_W_CS)
    OLED_W_DC                                0x080020d5   Thumb Code     8  driver_oled.o(i.OLED_W_DC)
    OLED_W_RES                               0x080020dd   Thumb Code     8  driver_oled.o(i.OLED_W_RES)
    OLED_WriteCommand                        0x080020e5   Thumb Code    36  driver_oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08002109   Thumb Code    42  driver_oled.o(i.OLED_WriteData)
    PendSV_Handler                           0x08002133   Thumb Code     4  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08002271   Thumb Code     4  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08002275   Thumb Code    24  stm32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x0800228d   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x0800229d   Thumb Code   174  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08002379   Thumb Code    26  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_OC2_SetConfig                        0x080023fd   Thumb Code   106  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    USART1_IRQHandler                        0x0800270d   Thumb Code    20  uart.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08002729   Thumb Code    24  uart.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08002749   Thumb Code    24  uart.o(i.USART3_IRQHandler)
    UsageFault_Handler                       0x08002769   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __0snprintf                              0x0800276d   Thumb Code    48  printfa.o(i.__0snprintf)
    __1snprintf                              0x0800276d   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x0800276d   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x0800276d   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x0800276d   Thumb Code     0  printfa.o(i.__0snprintf)
    __0vsnprintf                             0x080027a1   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x080027a1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x080027a1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x080027a1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x080027a1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __0vsprintf                              0x080027d5   Thumb Code    30  printfa.o(i.__0vsprintf)
    __1vsprintf                              0x080027d5   Thumb Code     0  printfa.o(i.__0vsprintf)
    __2vsprintf                              0x080027d5   Thumb Code     0  printfa.o(i.__0vsprintf)
    __c89vsprintf                            0x080027d5   Thumb Code     0  printfa.o(i.__0vsprintf)
    vsprintf                                 0x080027d5   Thumb Code     0  printfa.o(i.__0vsprintf)
    __scatterload_copy                       0x08002819   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08002827   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08002829   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    _sys_device_do_config_level              0x080030e5   Thumb Code    36  device.o(i._sys_device_do_config_level)
    adc_get_value                            0x0800310d   Thumb Code    40  adc.o(i.adc_get_value)
    adc_pin_init                             0x0800313d   Thumb Code   288  adc.o(i.adc_pin_init)
    app_event_init                           0x080032f5   Thumb Code    38  app_event.o(i.app_event_init)
    app_init                                 0x08003339   Thumb Code     4  sdk_main.o(i.app_init)
    app_msg_get_notify                       0x0800333d   Thumb Code    22  app_event.o(i.app_msg_get_notify)
    board_init                               0x08003407   Thumb Code    26  sdk_main.o(i.board_init)
    debug_error                              0x08003421   Thumb Code    84  debug.o(i.debug_error)
    debug_init                               0x08003489   Thumb Code    30  debug.o(i.debug_init)
    debug_message                            0x080034b1   Thumb Code    84  debug.o(i.debug_message)
    debug_printf                             0x08003519   Thumb Code    74  debug.o(i.debug_printf)
    debug_send_data                          0x080035c1   Thumb Code    12  debug.o(i.debug_send_data)
    debug_warning                            0x080035cd   Thumb Code    84  debug.o(i.debug_warning)
    delay_init                               0x08003639   Thumb Code     6  delay.o(i.delay_init)
    display_show_init                        0x08003645   Thumb Code     4  sdk_main.o(i.display_show_init)
    get_debug_time                           0x08003649   Thumb Code    70  debug.o(i.get_debug_time)
    get_temperature_mcu                      0x080036ad   Thumb Code    58  sdk_main.o(i.get_temperature_mcu)
    gpio_led_blue                            0x080036f9   Thumb Code    12  gpio_led.o(i.gpio_led_blue)
    gpio_led_green                           0x08003705   Thumb Code    12  gpio_led.o(i.gpio_led_green)
    gpio_led_init                            0x08003711   Thumb Code    24  gpio_led.o(i.gpio_led_init)
    gpio_led_off                             0x0800373d   Thumb Code    34  gpio_led.o(i.gpio_led_off)
    gpio_led_red                             0x0800375f   Thumb Code    12  gpio_led.o(i.gpio_led_red)
    gpio_led_run                             0x0800376b   Thumb Code    30  gpio_led.o(i.gpio_led_run)
    gpio_pin_init                            0x08003789   Thumb Code   366  gpio.o(i.gpio_pin_init)
    gpio_pin_init_alternate                  0x0800391d   Thumb Code   540  gpio.o(i.gpio_pin_init_alternate)
    gpio_write_pin                           0x08003b5d   Thumb Code    16  gpio.o(i.gpio_write_pin)
    iwdg_feed                                0x08003b71   Thumb Code     6  wdg.o(i.iwdg_feed)
    iwdg_feed_task                           0x08003b7d   Thumb Code    24  sdk_main.o(i.iwdg_feed_task)
    key_init                                 0x08003b99   Thumb Code     2  key.o(i.key_init)
    main                                     0x08003b9b   Thumb Code    30  main.o(i.main)
    process_key_event                        0x08003bb9   Thumb Code    18  key.o(i.process_key_event)
    pvPortMalloc                             0x08004389   Thumb Code   172  heap_4.o(i.pvPortMalloc)
    pxPortInitialiseStack                    0x08004439   Thumb Code    30  port.o(i.pxPortInitialiseStack)
    sdk_init                                 0x0800445d   Thumb Code    46  sdk_main.o(i.sdk_init)
    sdk_main                                 0x080044b1   Thumb Code   176  sdk_main.o(i.sdk_main)
    spi_pin_init                             0x080045c1   Thumb Code   408  spi.o(i.spi_pin_init)
    spi_send_data                            0x0800476d   Thumb Code    38  spi.o(i.spi_send_data)
    sys_check_rst                            0x08004799   Thumb Code   134  sys.o(i.sys_check_rst)
    sys_event_get_notify                     0x080048e1   Thumb Code    14  system_event.o(i.sys_event_get_notify)
    sys_event_init                           0x080048f5   Thumb Code    38  system_event.o(i.sys_event_init)
    sys_get_tick                             0x08004935   Thumb Code     4  sys.o(i.sys_get_tick)
    sys_stm32_clock_init                     0x08004939   Thumb Code   172  sys.o(i.sys_stm32_clock_init)
    system_init                              0x080049f1   Thumb Code    42  sdk_main.o(i.system_init)
    tim_pwm_pin_init                         0x08004a1d   Thumb Code   478  tim.o(i.tim_pwm_pin_init)
    tim_pwm_set_ccr                          0x08004c31   Thumb Code   164  tim.o(i.tim_pwm_set_ccr)
    uart_add_recv_callback                   0x08004ce1   Thumb Code     8  uart.o(i.uart_add_recv_callback)
    uart_init                                0x08004ced   Thumb Code   288  uart.o(i.uart_init)
    uart_send_data                           0x08004e21   Thumb Code    36  uart.o(i.uart_send_data)
    uxListRemove                             0x08004e49   Thumb Code    36  list.o(i.uxListRemove)
    vListInitialise                          0x08004e6d   Thumb Code    22  list.o(i.vListInitialise)
    vListInitialiseItem                      0x08004e83   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x08004e89   Thumb Code    48  list.o(i.vListInsert)
    vPortEnterCritical                       0x08004eb9   Thumb Code    24  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x08004ed5   Thumb Code    18  port.o(i.vPortExitCritical)
    vPortFree                                0x08004eed   Thumb Code    64  heap_4.o(i.vPortFree)
    vPortSetupTimerInterrupt                 0x08004f31   Thumb Code    20  port.o(i.vPortSetupTimerInterrupt)
    vQueueWaitForMessageRestricted           0x08004f49   Thumb Code    70  queue.o(i.vQueueWaitForMessageRestricted)
    vTaskDelay                               0x08004f91   Thumb Code    48  tasks.o(i.vTaskDelay)
    vTaskInternalSetTimeOutState             0x08004fc5   Thumb Code    12  tasks.o(i.vTaskInternalSetTimeOutState)
    vTaskMissedYield                         0x08004fd5   Thumb Code     8  tasks.o(i.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x08004fe1   Thumb Code    26  tasks.o(i.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x08005001   Thumb Code    62  tasks.o(i.vTaskPlaceOnEventListRestricted)
    vTaskStartScheduler                      0x08005045   Thumb Code    56  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x08005081   Thumb Code    10  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x08005091   Thumb Code    70  tasks.o(i.vTaskSwitchContext)
    xPortStartScheduler                      0x080050e1   Thumb Code    52  port.o(i.xPortStartScheduler)
    xPortSysTickHandler                      0x08005121   Thumb Code    38  port.o(i.xPortSysTickHandler)
    xQueueGenericCreate                      0x0800514d   Thumb Code    64  queue.o(i.xQueueGenericCreate)
    xQueueGenericReset                       0x0800518d   Thumb Code   142  queue.o(i.xQueueGenericReset)
    xQueueReceive                            0x08005221   Thumb Code   236  queue.o(i.xQueueReceive)
    xTaskCheckForTimeOut                     0x08005311   Thumb Code    84  tasks.o(i.xTaskCheckForTimeOut)
    xTaskCreate                              0x08005369   Thumb Code    30  tasks.o(i.xTaskCreate)
    xTaskDelayUntil                          0x08005389   Thumb Code    84  tasks.o(i.xTaskDelayUntil)
    xTaskGetSchedulerState                   0x080053e5   Thumb Code    24  tasks.o(i.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x08005401   Thumb Code     6  tasks.o(i.xTaskGetTickCount)
    xTaskIncrementTick                       0x0800540d   Thumb Code   278  tasks.o(i.xTaskIncrementTick)
    xTaskRemoveFromEventList                 0x0800552d   Thumb Code   196  tasks.o(i.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x080055f9   Thumb Code   286  tasks.o(i.xTaskResumeAll)
    xTimerCreateTimerTask                    0x08005725   Thumb Code    42  timers.o(i.xTimerCreateTimerTask)
    AHBPrescTable                            0x08005760   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08005770   Data           8  system_stm32f4xx.o(.constdata)
    gpio_map                                 0x080058c0   Data         896  gpio.o(.constdata)
    OLED_F8x16                               0x08005c40   Data        1520  driver_oled_data.o(.constdata)
    OLED_F6x8                                0x08006230   Data         570  driver_oled_data.o(.constdata)
    OLED_CF16x16                             0x0800646a   Data         259  driver_oled_data.o(.constdata)
    Region$$Table$$Base                      0x08006588   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080065b8   Number         0  anon$$obj.o(Region$$Table)
    Image$$DEVEICE_DRIVER_CORE_REGISTRY$$Base 0x20000000   Number         0  anon$$obj.o ABSOLUTE
    Image$$DEVEICE_DRIVER_CORE_REGISTRY$$Limit 0x20000000   Number         0  anon$$obj.o ABSOLUTE
    Image$$DEVEICE_DRIVER_REGISTRY$$Base     0x20000100   Number         0  anon$$obj.o ABSOLUTE
    Image$$DEVEICE_DRIVER_REGISTRY$$Limit    0x2000010c   Number         0  anon$$obj.o ABSOLUTE
    Image$$DEVEICE_MIDDWARE_REGISTRY$$Base   0x20000200   Number         0  anon$$obj.o ABSOLUTE
    Image$$DEVEICE_MIDDWARE_REGISTRY$$Limit  0x20000200   Number         0  anon$$obj.o ABSOLUTE
    Image$$DEVEICE_APPLICATION_REGISTRY$$Base 0x20000300   Number         0  anon$$obj.o ABSOLUTE
    Image$$DEVEICE_APPLICATION_REGISTRY$$Limit 0x20000300   Number         0  anon$$obj.o ABSOLUTE
    uwTickFreq                               0x20000400   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000404   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000408   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000040c   Data           4  system_stm32f4xx.o(.data)
    pxCurrentTCB                             0x20000434   Data           4  tasks.o(.data)
    _gpio_led_config                         0x200004c8   Data           4  gpio_led.o(.data)
    _gpio_led_api                            0x200004cc   Data          20  gpio_led.o(.data)
    debug_time_str                           0x200008e4   Data          32  debug.o(.bss)
    spi1                                     0x20001fe0   Data          88  spi.o(.bss)
    spi2                                     0x20002038   Data          88  spi.o(.bss)
    spi3                                     0x20002090   Data          88  spi.o(.bss)
    tim1_pwm_handle                          0x200020e8   Data          72  tim.o(.bss)
    tim2_pwm_handle                          0x20002130   Data          72  tim.o(.bss)
    tim3_pwm_handle                          0x20002178   Data          72  tim.o(.bss)
    tim4_pwm_handle                          0x200021c0   Data          72  tim.o(.bss)
    tim5_pwm_handle                          0x20002208   Data          72  tim.o(.bss)
    tim6_pwm_handle                          0x20002250   Data          72  tim.o(.bss)
    tim7_pwm_handle                          0x20002298   Data          72  tim.o(.bss)
    tim8_pwm_handle                          0x200022e0   Data          72  tim.o(.bss)
    tim9_pwm_handle                          0x20002328   Data          72  tim.o(.bss)
    tim10_pwm_handle                         0x20002370   Data          72  tim.o(.bss)
    g_uart_recv_cbs                          0x20002490   Data          16  uart.o(.bss)
    iwdg_handler                             0x200024a0   Data          12  wdg.o(.bss)
    OLED_DisplayBuf                          0x200024ac   Data        1024  driver_oled.o(.bss)
    __initial_sp                             0x20002cb0   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000066a8, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000065b8, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         6110  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         6479    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         6482    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         6484    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         6486    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         6487    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         6494    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         6489    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         6491    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         6480    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x000000be   Code   RO         4571    .emb_text           port.o
    0x0800025e   0x0800025e   0x00000002   PAD
    0x08000260   0x08000260   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000284   0x08000284   0x00000062   Code   RO         6113    .text               mc_w.l(uldiv.o)
    0x080002e6   0x080002e6   0x00000024   Code   RO         6117    .text               mc_w.l(memcpya.o)
    0x0800030a   0x0800030a   0x00000024   Code   RO         6119    .text               mc_w.l(memseta.o)
    0x0800032e   0x0800032e   0x0000000e   Code   RO         6121    .text               mc_w.l(strlen.o)
    0x0800033c   0x0800033c   0x0000001c   Code   RO         6123    .text               mc_w.l(strcmp.o)
    0x08000358   0x08000358   0x0000014e   Code   RO         6426    .text               mf_w.l(dadd.o)
    0x080004a6   0x080004a6   0x000000e4   Code   RO         6428    .text               mf_w.l(dmul.o)
    0x0800058a   0x0800058a   0x000000de   Code   RO         6430    .text               mf_w.l(ddiv.o)
    0x08000668   0x08000668   0x0000001a   Code   RO         6436    .text               mf_w.l(dfltui.o)
    0x08000682   0x08000682   0x00000026   Code   RO         6442    .text               mf_w.l(f2d.o)
    0x080006a8   0x080006a8   0x00000030   Code   RO         6446    .text               mf_w.l(cdrcmple.o)
    0x080006d8   0x080006d8   0x00000038   Code   RO         6448    .text               mf_w.l(d2f.o)
    0x08000710   0x08000710   0x0000002c   Code   RO         6498    .text               mc_w.l(uidiv.o)
    0x0800073c   0x0800073c   0x0000001e   Code   RO         6500    .text               mc_w.l(llshl.o)
    0x0800075a   0x0800075a   0x00000020   Code   RO         6502    .text               mc_w.l(llushr.o)
    0x0800077a   0x0800077a   0x00000024   Code   RO         6504    .text               mc_w.l(llsshr.o)
    0x0800079e   0x0800079e   0x00000000   Code   RO         6521    .text               mc_w.l(iusefp.o)
    0x0800079e   0x0800079e   0x0000006e   Code   RO         6522    .text               mf_w.l(fepilogue.o)
    0x0800080c   0x0800080c   0x000000ba   Code   RO         6524    .text               mf_w.l(depilogue.o)
    0x080008c6   0x080008c6   0x00000030   Code   RO         6530    .text               mf_w.l(dfixul.o)
    0x080008f6   0x080008f6   0x00000002   PAD
    0x080008f8   0x080008f8   0x00000024   Code   RO         6536    .text               mc_w.l(init.o)
    0x0800091c   0x0800091c   0x00000130   Code   RO         1368    i.ADC_Init          stm32f4xx_hal_adc.o
    0x08000a4c   0x08000a4c   0x00000002   Code   RO         3009    i.BusFault_Handler  stm32f4xx_it.o
    0x08000a4e   0x08000a4e   0x00000002   Code   RO         3010    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000a50   0x08000a50   0x00000158   Code   RO         1370    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x08000ba8   0x08000ba8   0x00000006   Code   RO         1377    i.HAL_ADC_GetValue  stm32f4xx_hal_adc.o
    0x08000bae   0x08000bae   0x00000054   Code   RO         1379    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x08000c02   0x08000c02   0x00000002   Code   RO         1382    i.HAL_ADC_MspInit   stm32f4xx_hal_adc.o
    0x08000c04   0x08000c04   0x000000aa   Code   RO         1383    i.HAL_ADC_PollForConversion  stm32f4xx_hal_adc.o
    0x08000cae   0x08000cae   0x00000002   PAD
    0x08000cb0   0x08000cb0   0x00000114   Code   RO         1385    i.HAL_ADC_Start     stm32f4xx_hal_adc.o
    0x08000dc4   0x08000dc4   0x0000003c   Code   RO         1388    i.HAL_ADC_Stop      stm32f4xx_hal_adc.o
    0x08000e00   0x08000e00   0x000001d0   Code   RO          352    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08000fd0   0x08000fd0   0x0000000e   Code   RO          356    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08000fde   0x08000fde   0x00000002   PAD
    0x08000fe0   0x08000fe0   0x0000000c   Code   RO           25    i.HAL_GetREVID      stm32f4xx_hal.o
    0x08000fec   0x08000fec   0x0000000c   Code   RO           26    i.HAL_GetTick       stm32f4xx_hal.o
    0x08000ff8   0x08000ff8   0x0000000c   Code   RO         1641    i.HAL_IWDG_Refresh  stm32f4xx_hal_iwdg.o
    0x08001004   0x08001004   0x00000010   Code   RO           32    i.HAL_IncTick       stm32f4xx_hal.o
    0x08001014   0x08001014   0x0000002c   Code   RO           33    i.HAL_Init          stm32f4xx_hal.o
    0x08001040   0x08001040   0x00000044   Code   RO           34    i.HAL_InitTick      stm32f4xx_hal.o
    0x08001084   0x08001084   0x00000002   Code   RO           36    i.HAL_MspInit       stm32f4xx_hal.o
    0x08001086   0x08001086   0x0000001a   Code   RO         1124    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x080010a0   0x080010a0   0x00000040   Code   RO         1130    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080010e0   0x080010e0   0x00000024   Code   RO         1131    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001104   0x08001104   0x0000014c   Code   RO          590    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08001250   0x08001250   0x0000000c   Code   RO          595    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x0800125c   0x0800125c   0x00000020   Code   RO          597    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x0800127c   0x0800127c   0x00000020   Code   RO          598    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x0800129c   0x0800129c   0x00000068   Code   RO          599    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08001304   0x08001304   0x000003c0   Code   RO          602    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080016c4   0x080016c4   0x000000be   Code   RO         1681    i.HAL_SPI_Init      stm32f4xx_hal_spi.o
    0x08001782   0x08001782   0x00000002   Code   RO         1683    i.HAL_SPI_MspInit   stm32f4xx_hal_spi.o
    0x08001784   0x08001784   0x00000194   Code   RO         1689    i.HAL_SPI_Transmit  stm32f4xx_hal_spi.o
    0x08001918   0x08001918   0x00000028   Code   RO         1135    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08001940   0x08001940   0x000000d8   Code   RO         2066    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08001a18   0x08001a18   0x0000004a   Code   RO         2069    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08001a62   0x08001a62   0x00000002   Code   RO         2071    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08001a64   0x08001a64   0x000000dc   Code   RO         2074    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x08001b40   0x08001b40   0x00000066   Code   RO          775    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08001ba6   0x08001ba6   0x00000002   Code   RO          777    i.HAL_UART_MspInit  stm32f4xx_hal_uart.o
    0x08001ba8   0x08001ba8   0x000000ae   Code   RO          783    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08001c56   0x08001c56   0x00000002   Code   RO         3011    i.HardFault_Handler  stm32f4xx_it.o
    0x08001c58   0x08001c58   0x00000002   Code   RO         3012    i.MemManage_Handler  stm32f4xx_it.o
    0x08001c5a   0x08001c5a   0x00000002   Code   RO         3013    i.NMI_Handler       stm32f4xx_it.o
    0x08001c5c   0x08001c5c   0x00000028   Code   RO         5841    i.OLED_Clear        driver_oled.o
    0x08001c84   0x08001c84   0x0000005c   Code   RO         5842    i.OLED_ClearArea    driver_oled.o
    0x08001ce0   0x08001ce0   0x00000062   Code   RO         5850    i.OLED_GPIO_Init    driver_oled.o
    0x08001d42   0x08001d42   0x0000009c   Code   RO         5852    i.OLED_Init         driver_oled.o
    0x08001dde   0x08001dde   0x0000002a   Code   RO         5855    i.OLED_Printf       driver_oled.o
    0x08001e08   0x08001e08   0x00000022   Code   RO         5858    i.OLED_SetCursor    driver_oled.o
    0x08001e2a   0x08001e2a   0x00000002   PAD
    0x08001e2c   0x08001e2c   0x0000003c   Code   RO         5860    i.OLED_ShowChar     driver_oled.o
    0x08001e68   0x08001e68   0x000000b4   Code   RO         5863    i.OLED_ShowImage    driver_oled.o
    0x08001f1c   0x08001f1c   0x00000188   Code   RO         5866    i.OLED_ShowString   driver_oled.o
    0x080020a4   0x080020a4   0x00000028   Code   RO         5867    i.OLED_Update       driver_oled.o
    0x080020cc   0x080020cc   0x00000008   Code   RO         5869    i.OLED_W_CS         driver_oled.o
    0x080020d4   0x080020d4   0x00000008   Code   RO         5870    i.OLED_W_DC         driver_oled.o
    0x080020dc   0x080020dc   0x00000008   Code   RO         5871    i.OLED_W_RES        driver_oled.o
    0x080020e4   0x080020e4   0x00000024   Code   RO         5872    i.OLED_WriteCommand  driver_oled.o
    0x08002108   0x08002108   0x0000002a   Code   RO         5873    i.OLED_WriteData    driver_oled.o
    0x08002132   0x08002132   0x00000004   Code   RO         3014    i.PendSV_Handler    stm32f4xx_it.o
    0x08002136   0x08002136   0x00000002   PAD
    0x08002138   0x08002138   0x0000007c   Code   RO         1719    i.SPI_EndRxTxTransaction  stm32f4xx_hal_spi.o
    0x080021b4   0x080021b4   0x000000bc   Code   RO         1724    i.SPI_WaitFlagStateUntilTimeout  stm32f4xx_hal_spi.o
    0x08002270   0x08002270   0x00000004   Code   RO         3015    i.SVC_Handler       stm32f4xx_it.o
    0x08002274   0x08002274   0x00000018   Code   RO         3016    i.SysTick_Handler   stm32f4xx_it.o
    0x0800228c   0x0800228c   0x00000010   Code   RO         3121    i.SystemInit        system_stm32f4xx.o
    0x0800229c   0x0800229c   0x000000dc   Code   RO         2087    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08002378   0x08002378   0x0000001a   Code   RO         2088    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x08002392   0x08002392   0x00000002   PAD
    0x08002394   0x08002394   0x00000068   Code   RO         2100    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x080023fc   0x080023fc   0x00000074   Code   RO         2101    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x08002470   0x08002470   0x00000070   Code   RO         2102    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x080024e0   0x080024e0   0x00000054   Code   RO         2103    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x08002534   0x08002534   0x0000004e   Code   RO          798    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08002582   0x08002582   0x00000002   PAD
    0x08002584   0x08002584   0x00000114   Code   RO          802    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08002698   0x08002698   0x00000074   Code   RO          806    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x0800270c   0x0800270c   0x0000001c   Code   RO         5072    i.USART1_IRQHandler  uart.o
    0x08002728   0x08002728   0x00000020   Code   RO         5073    i.USART2_IRQHandler  uart.o
    0x08002748   0x08002748   0x00000020   Code   RO         5074    i.USART3_IRQHandler  uart.o
    0x08002768   0x08002768   0x00000002   Code   RO         3017    i.UsageFault_Handler  stm32f4xx_it.o
    0x0800276a   0x0800276a   0x00000002   PAD
    0x0800276c   0x0800276c   0x00000034   Code   RO         6365    i.__0snprintf       mc_w.l(printfa.o)
    0x080027a0   0x080027a0   0x00000034   Code   RO         6369    i.__0vsnprintf      mc_w.l(printfa.o)
    0x080027d4   0x080027d4   0x00000024   Code   RO         6370    i.__0vsprintf       mc_w.l(printfa.o)
    0x080027f8   0x080027f8   0x00000020   Code   RO         1137    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002818   0x08002818   0x0000000e   Code   RO         6549    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08002826   0x08002826   0x00000002   Code   RO         6550    i.__scatterload_null  mc_w.l(handlers.o)
    0x08002828   0x08002828   0x0000000e   Code   RO         6551    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08002836   0x08002836   0x00000002   PAD
    0x08002838   0x08002838   0x00000184   Code   RO         6371    i._fp_digits        mc_w.l(printfa.o)
    0x080029bc   0x080029bc   0x000006b4   Code   RO         6372    i._printf_core      mc_w.l(printfa.o)
    0x08003070   0x08003070   0x00000024   Code   RO         6373    i._printf_post_padding  mc_w.l(printfa.o)
    0x08003094   0x08003094   0x0000002e   Code   RO         6374    i._printf_pre_padding  mc_w.l(printfa.o)
    0x080030c2   0x080030c2   0x00000016   Code   RO         6375    i._snputc           mc_w.l(printfa.o)
    0x080030d8   0x080030d8   0x0000000a   Code   RO         6376    i._sputc            mc_w.l(printfa.o)
    0x080030e2   0x080030e2   0x00000002   PAD
    0x080030e4   0x080030e4   0x00000028   Code   RO         4715    i._sys_device_do_config_level  device.o
    0x0800310c   0x0800310c   0x00000030   Code   RO         4836    i.adc_get_value     adc.o
    0x0800313c   0x0800313c   0x0000016c   Code   RO         4837    i.adc_pin_init      adc.o
    0x080032a8   0x080032a8   0x0000004c   Code   RO         4838    i.adc_read_task     adc.o
    0x080032f4   0x080032f4   0x00000044   Code   RO         3374    i.app_event_init    app_event.o
    0x08003338   0x08003338   0x00000004   Code   RO         3484    i.app_init          sdk_main.o
    0x0800333c   0x0800333c   0x0000001c   Code   RO         3375    i.app_msg_get_notify  app_event.o
    0x08003358   0x08003358   0x000000a4   Code   RO         3376    i.app_msg_handle    app_event.o
    0x080033fc   0x080033fc   0x0000000a   Code   RO         3378    i.app_process_msg   app_event.o
    0x08003406   0x08003406   0x0000001a   Code   RO         3485    i.board_init        sdk_main.o
    0x08003420   0x08003420   0x00000068   Code   RO         3258    i.debug_error       debug.o
    0x08003488   0x08003488   0x00000028   Code   RO         3259    i.debug_init        debug.o
    0x080034b0   0x080034b0   0x00000068   Code   RO         3260    i.debug_message     debug.o
    0x08003518   0x08003518   0x00000054   Code   RO         3261    i.debug_printf      debug.o
    0x0800356c   0x0800356c   0x00000054   Code   RO         3262    i.debug_recv_callback  debug.o
    0x080035c0   0x080035c0   0x0000000c   Code   RO         3263    i.debug_send_data   debug.o
    0x080035cc   0x080035cc   0x0000006c   Code   RO         3265    i.debug_warning     debug.o
    0x08003638   0x08003638   0x0000000c   Code   RO         3215    i.delay_init        delay.o
    0x08003644   0x08003644   0x00000004   Code   RO         3486    i.display_show_init  sdk_main.o
    0x08003648   0x08003648   0x00000064   Code   RO         3266    i.get_debug_time    debug.o
    0x080036ac   0x080036ac   0x0000004c   Code   RO         3487    i.get_temperature_mcu  sdk_main.o
    0x080036f8   0x080036f8   0x0000000c   Code   RO         4743    i.gpio_led_blue     gpio_led.o
    0x08003704   0x08003704   0x0000000c   Code   RO         4744    i.gpio_led_green    gpio_led.o
    0x08003710   0x08003710   0x0000002c   Code   RO         4745    i.gpio_led_init     gpio_led.o
    0x0800373c   0x0800373c   0x00000022   Code   RO         4746    i.gpio_led_off      gpio_led.o
    0x0800375e   0x0800375e   0x0000000c   Code   RO         4747    i.gpio_led_red      gpio_led.o
    0x0800376a   0x0800376a   0x0000001e   Code   RO         4748    i.gpio_led_run      gpio_led.o
    0x08003788   0x08003788   0x00000194   Code   RO         4875    i.gpio_pin_init     gpio.o
    0x0800391c   0x0800391c   0x00000240   Code   RO         4876    i.gpio_pin_init_alternate  gpio.o
    0x08003b5c   0x08003b5c   0x00000014   Code   RO         4879    i.gpio_write_pin    gpio.o
    0x08003b70   0x08003b70   0x0000000c   Code   RO         5129    i.iwdg_feed         wdg.o
    0x08003b7c   0x08003b7c   0x0000001c   Code   RO         3488    i.iwdg_feed_task    sdk_main.o
    0x08003b98   0x08003b98   0x00000002   Code   RO         4645    i.key_init          key.o
    0x08003b9a   0x08003b9a   0x0000001e   Code   RO         2976    i.main              main.o
    0x08003bb8   0x08003bb8   0x00000034   Code   RO         4646    i.process_key_event  key.o
    0x08003bec   0x08003bec   0x00000022   Code   RO         3432    i.process_sys_event  system_event.o
    0x08003c0e   0x08003c0e   0x00000002   PAD
    0x08003c10   0x08003c10   0x00000094   Code   RO         4016    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x08003ca4   0x08003ca4   0x000000a8   Code   RO         4017    i.prvAddNewTaskToReadyList  tasks.o
    0x08003d4c   0x08003d4c   0x00000040   Code   RO         4348    i.prvCheckForValidListAndQueue  timers.o
    0x08003d8c   0x08003d8c   0x0000003c   Code   RO         4018    i.prvCheckTasksWaitingTermination  tasks.o
    0x08003dc8   0x08003dc8   0x00000028   Code   RO         3706    i.prvCopyDataFromQueue  queue.o
    0x08003df0   0x08003df0   0x00000050   Code   RO         4019    i.prvCreateIdleTasks  tasks.o
    0x08003e40   0x08003e40   0x0000005c   Code   RO         4020    i.prvCreateTask     tasks.o
    0x08003e9c   0x08003e9c   0x00000014   Code   RO         4021    i.prvDeleteTCB      tasks.o
    0x08003eb0   0x08003eb0   0x00000024   Code   RO         4349    i.prvGetNextExpireTime  timers.o
    0x08003ed4   0x08003ed4   0x00000048   Code   RO         4504    i.prvHeapInit       heap_4.o
    0x08003f1c   0x08003f1c   0x00000028   Code   RO         4022    i.prvIdleTask       tasks.o
    0x08003f44   0x08003f44   0x00000018   Code   RO         3708    i.prvInitialiseNewQueue  queue.o
    0x08003f5c   0x08003f5c   0x0000006e   Code   RO         4023    i.prvInitialiseNewTask  tasks.o
    0x08003fca   0x08003fca   0x00000002   PAD
    0x08003fcc   0x08003fcc   0x00000058   Code   RO         4024    i.prvInitialiseTaskLists  tasks.o
    0x08004024   0x08004024   0x00000054   Code   RO         4505    i.prvInsertBlockIntoFreeList  heap_4.o
    0x08004078   0x08004078   0x00000040   Code   RO         4351    i.prvInsertTimerInActiveList  timers.o
    0x080040b8   0x080040b8   0x0000001a   Code   RO         3709    i.prvIsQueueEmpty   queue.o
    0x080040d2   0x080040d2   0x00000002   PAD
    0x080040d4   0x080040d4   0x00000040   Code   RO         4352    i.prvProcessExpiredTimer  timers.o
    0x08004114   0x08004114   0x000000d8   Code   RO         4353    i.prvProcessReceivedCommands  timers.o
    0x080041ec   0x080041ec   0x0000006c   Code   RO         4354    i.prvProcessTimerOrBlockTask  timers.o
    0x08004258   0x08004258   0x00000028   Code   RO         4355    i.prvReloadTimer    timers.o
    0x08004280   0x08004280   0x00000020   Code   RO         4025    i.prvResetNextTaskUnblockTime  tasks.o
    0x080042a0   0x080042a0   0x0000002c   Code   RO         4356    i.prvSampleTimeNow  timers.o
    0x080042cc   0x080042cc   0x0000002c   Code   RO         4357    i.prvSwitchTimerLists  timers.o
    0x080042f8   0x080042f8   0x00000010   Code   RO         4572    i.prvTaskExitError  port.o
    0x08004308   0x08004308   0x00000014   Code   RO         4358    i.prvTimerTask      timers.o
    0x0800431c   0x0800431c   0x0000006a   Code   RO         3711    i.prvUnlockQueue    queue.o
    0x08004386   0x08004386   0x00000002   PAD
    0x08004388   0x08004388   0x000000b0   Code   RO         4507    i.pvPortMalloc      heap_4.o
    0x08004438   0x08004438   0x00000024   Code   RO         4573    i.pxPortInitialiseStack  port.o
    0x0800445c   0x0800445c   0x00000054   Code   RO         3489    i.sdk_init          sdk_main.o
    0x080044b0   0x080044b0   0x00000110   Code   RO         3490    i.sdk_main          sdk_main.o
    0x080045c0   0x080045c0   0x000001ac   Code   RO         5000    i.spi_pin_init      spi.o
    0x0800476c   0x0800476c   0x0000002c   Code   RO         5002    i.spi_send_data     spi.o
    0x08004798   0x08004798   0x00000148   Code   RO         3158    i.sys_check_rst     sys.o
    0x080048e0   0x080048e0   0x00000014   Code   RO         3433    i.sys_event_get_notify  system_event.o
    0x080048f4   0x080048f4   0x00000040   Code   RO         3434    i.sys_event_init    system_event.o
    0x08004934   0x08004934   0x00000004   Code   RO         3159    i.sys_get_tick      sys.o
    0x08004938   0x08004938   0x000000b8   Code   RO         3162    i.sys_stm32_clock_init  sys.o
    0x080049f0   0x080049f0   0x0000002a   Code   RO         3491    i.system_init       sdk_main.o
    0x08004a1a   0x08004a1a   0x00000002   PAD
    0x08004a1c   0x08004a1c   0x00000214   Code   RO         5039    i.tim_pwm_pin_init  tim.o
    0x08004c30   0x08004c30   0x000000b0   Code   RO         5040    i.tim_pwm_set_ccr   tim.o
    0x08004ce0   0x08004ce0   0x0000000c   Code   RO         5075    i.uart_add_recv_callback  uart.o
    0x08004cec   0x08004cec   0x00000134   Code   RO         5076    i.uart_init         uart.o
    0x08004e20   0x08004e20   0x00000028   Code   RO         5077    i.uart_send_data    uart.o
    0x08004e48   0x08004e48   0x00000024   Code   RO         3667    i.uxListRemove      list.o
    0x08004e6c   0x08004e6c   0x00000016   Code   RO         3668    i.vListInitialise   list.o
    0x08004e82   0x08004e82   0x00000006   Code   RO         3669    i.vListInitialiseItem  list.o
    0x08004e88   0x08004e88   0x00000030   Code   RO         3670    i.vListInsert       list.o
    0x08004eb8   0x08004eb8   0x0000001c   Code   RO         4575    i.vPortEnterCritical  port.o
    0x08004ed4   0x08004ed4   0x00000018   Code   RO         4576    i.vPortExitCritical  port.o
    0x08004eec   0x08004eec   0x00000044   Code   RO         4508    i.vPortFree         heap_4.o
    0x08004f30   0x08004f30   0x00000018   Code   RO         4577    i.vPortSetupTimerInterrupt  port.o
    0x08004f48   0x08004f48   0x00000046   Code   RO         3718    i.vQueueWaitForMessageRestricted  queue.o
    0x08004f8e   0x08004f8e   0x00000002   PAD
    0x08004f90   0x08004f90   0x00000034   Code   RO         4033    i.vTaskDelay        tasks.o
    0x08004fc4   0x08004fc4   0x00000010   Code   RO         4037    i.vTaskInternalSetTimeOutState  tasks.o
    0x08004fd4   0x08004fd4   0x0000000c   Code   RO         4038    i.vTaskMissedYield  tasks.o
    0x08004fe0   0x08004fe0   0x00000020   Code   RO         4039    i.vTaskPlaceOnEventList  tasks.o
    0x08005000   0x08005000   0x00000044   Code   RO         4040    i.vTaskPlaceOnEventListRestricted  tasks.o
    0x08005044   0x08005044   0x0000003c   Code   RO         4047    i.vTaskStartScheduler  tasks.o
    0x08005080   0x08005080   0x00000010   Code   RO         4049    i.vTaskSuspendAll   tasks.o
    0x08005090   0x08005090   0x00000050   Code   RO         4050    i.vTaskSwitchContext  tasks.o
    0x080050e0   0x080050e0   0x00000040   Code   RO         4578    i.xPortStartScheduler  port.o
    0x08005120   0x08005120   0x0000002c   Code   RO         4579    i.xPortSysTickHandler  port.o
    0x0800514c   0x0800514c   0x00000040   Code   RO         3719    i.xQueueGenericCreate  queue.o
    0x0800518c   0x0800518c   0x00000094   Code   RO         3720    i.xQueueGenericReset  queue.o
    0x08005220   0x08005220   0x000000f0   Code   RO         3728    i.xQueueReceive     queue.o
    0x08005310   0x08005310   0x00000058   Code   RO         4052    i.xTaskCheckForTimeOut  tasks.o
    0x08005368   0x08005368   0x0000001e   Code   RO         4053    i.xTaskCreate       tasks.o
    0x08005386   0x08005386   0x00000002   PAD
    0x08005388   0x08005388   0x0000005c   Code   RO         4054    i.xTaskDelayUntil   tasks.o
    0x080053e4   0x080053e4   0x0000001c   Code   RO         4061    i.xTaskGetSchedulerState  tasks.o
    0x08005400   0x08005400   0x0000000c   Code   RO         4062    i.xTaskGetTickCount  tasks.o
    0x0800540c   0x0800540c   0x00000120   Code   RO         4064    i.xTaskIncrementTick  tasks.o
    0x0800552c   0x0800552c   0x000000cc   Code   RO         4065    i.xTaskRemoveFromEventList  tasks.o
    0x080055f8   0x080055f8   0x0000012c   Code   RO         4066    i.xTaskResumeAll    tasks.o
    0x08005724   0x08005724   0x0000003c   Code   RO         4365    i.xTimerCreateTimerTask  timers.o
    0x08005760   0x08005760   0x00000010   Data   RO         3122    .constdata          system_stm32f4xx.o
    0x08005770   0x08005770   0x00000008   Data   RO         3123    .constdata          system_stm32f4xx.o
    0x08005778   0x08005778   0x0000000e   Data   RO         3163    .constdata          sys.o
    0x08005786   0x08005786   0x0000001f   Data   RO         3268    .constdata          debug.o
    0x080057a5   0x080057a5   0x0000000f   Data   RO         3379    .constdata          app_event.o
    0x080057b4   0x080057b4   0x00000009   Data   RO         3492    .constdata          sdk_main.o
    0x080057bd   0x080057bd   0x00000012   Data   RO         4647    .constdata          key.o
    0x080057cf   0x080057cf   0x00000001   PAD
    0x080057d0   0x080057d0   0x000000ef   Data   RO         4840    .constdata          adc.o
    0x080058bf   0x080058bf   0x00000001   PAD
    0x080058c0   0x080058c0   0x00000380   Data   RO         4880    .constdata          gpio.o
    0x08005c40   0x08005c40   0x000005f0   Data   RO         6067    .constdata          driver_oled_data.o
    0x08006230   0x08006230   0x0000023a   Data   RO         6068    .constdata          driver_oled_data.o
    0x0800646a   0x0800646a   0x00000103   Data   RO         6069    .constdata          driver_oled_data.o
    0x0800656d   0x0800656d   0x00000003   PAD
    0x08006570   0x08006570   0x00000009   Data   RO         4749    .conststrlit        gpio_led.o
    0x08006579   0x08006579   0x00000003   PAD
    0x0800657c   0x0800657c   0x0000000c   Data   RO         4752    .devconfig.init     gpio_led.o
    0x08006588   0x08006588   0x00000030   Data   RO         6547    Region$$Table       anon$$obj.o


    Execution Region DEVEICE_DRIVER_CORE_REGISTRY (Exec base: 0x20000000, Load base: 0x080065b8, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region DEVEICE_DRIVER_REGISTRY (Exec base: 0x20000100, Load base: 0x080065b8, Size: 0x0000000c, Max: 0x00000100, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000100   0x080065b8   0x0000000c   Data   RO         4753    .device_DRIVER      gpio_led.o


    Execution Region DEVEICE_MIDDWARE_REGISTRY (Exec base: 0x20000200, Load base: 0x080065c4, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region DEVEICE_APPLICATION_REGISTRY (Exec base: 0x20000300, Load base: 0x080065c4, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000400, Load base: 0x080065c4, Size: 0x000028b0, Max: 0x0001fc00, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000400   0x080065c4   0x0000000c   Data   RW           40    .data               stm32f4xx_hal.o
    0x2000040c   0x080065d0   0x00000004   Data   RW         3124    .data               system_stm32f4xx.o
    0x20000410   0x080065d4   0x00000004   Data   RW         3218    .data               delay.o
    0x20000414   0x080065d8   0x00000002   Data   RW         3269    .data               debug.o
    0x20000416   0x080065da   0x00000002   PAD
    0x20000418   0x080065dc   0x00000008   Data   RW         3380    .data               app_event.o
    0x20000420   0x080065e4   0x00000008   Data   RW         3436    .data               system_event.o
    0x20000428   0x080065ec   0x0000000c   Data   RW         3493    .data               sdk_main.o
    0x20000434   0x080065f8   0x00000040   Data   RW         4069    .data               tasks.o
    0x20000474   0x08006638   0x00000014   Data   RW         4374    .data               timers.o
    0x20000488   0x0800664c   0x0000001c   Data   RW         4515    .data               heap_4.o
    0x200004a4   0x08006668   0x00000004   Data   RW         4580    .data               port.o
    0x200004a8   0x0800666c   0x00000020   Data   RW         4717    .data               device.o
    0x200004c8   0x0800668c   0x00000004   Data   RW         4750    .data               gpio_led.o
    0x200004cc   0x08006690   0x00000014   Data   RW         4751    .data               gpio_led.o
    0x200004e0   0x080066a4   0x00000003   Data   RW         4841    .data               adc.o
    0x200004e3   0x080066a7   0x00000001   PAD
    0x200004e4        -       0x000004a0   Zero   RW         3267    .bss                debug.o
    0x20000984        -       0x0000012c   Zero   RW         4068    .bss                tasks.o
    0x20000ab0        -       0x00000028   Zero   RW         4373    .bss                timers.o
    0x20000ad8        -       0x00001400   Zero   RW         4514    .bss                heap_4.o
    0x20001ed8        -       0x00000108   Zero   RW         4839    .bss                adc.o
    0x20001fe0        -       0x00000108   Zero   RW         5003    .bss                spi.o
    0x200020e8        -       0x000002d0   Zero   RW         5041    .bss                tim.o
    0x200023b8        -       0x000000e8   Zero   RW         5078    .bss                uart.o
    0x200024a0        -       0x0000000c   Zero   RW         5131    .bss                wdg.o
    0x200024ac        -       0x00000400   Zero   RW         5875    .bss                driver_oled.o
    0x200028ac   0x080066a7   0x00000004   PAD
    0x200028b0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       488         92        239          3        264       3359   adc.o
       270        114         15          8          0       5062   app_event.o
       636        132         31          2       1184       6727   debug.o
        12          6          0          4          0        701   delay.o
        40          4          0         32          0       2144   device.o
      1236         38          0          0       1024      11623   driver_oled.o
         0          0       2349          0          0       1278   driver_oled_data.o
         0          0          0          0          0      15476   event_groups.o
      1000         78        896          0          0       4058   gpio.o
       144         20         33         24          0       4755   gpio_led.o
       400         24          0         28       5120       5327   heap_4.o
        54         34         18          0          0       1611   key.o
       112          0          0          0          0       2985   list.o
        30          0          0          0          0        535   main.o
       426         54          0          4          0      10084   port.o
       718         10          0          0          0      11172   queue.o
       536        156          9         12          0       5715   sdk_main.o
       472         26          0          0        264       2620   spi.o
        36          8        392          0       1024       1004   startup_stm32f407xx.o
       154         28          0         12          0     723370   stm32f4xx_hal.o
      1246         48          0          0          0       6951   stm32f4xx_hal_adc.o
       198         14          0          0          0      34363   stm32f4xx_hal_cortex.o
       478         44          0          0          0       2500   stm32f4xx_hal_gpio.o
        12          0          0          0          0       1255   stm32f4xx_hal_iwdg.o
      1472         76          0          0          0       6760   stm32f4xx_hal_rcc.o
       908         14          0          0          0       5432   stm32f4xx_hal_spi.o
      1174        112          0          0          0       9022   stm32f4xx_hal_tim.o
       748         10          0          0          0       5695   stm32f4xx_hal_uart.o
        44          0          0          0          0      27230   stm32f4xx_it.o
       516        206         14          0          0       2518   sys.o
       118         32          0          8          0       2936   system_event.o
        16          4         24          4          0       1323   system_stm32f4xx.o
      2216        170          0         64        300      37650   tasks.o
       708         66          0          0        720       3536   tim.o
       760         78          0         20         40      12020   timers.o
       452         52          0          0        232       4856   uart.o
        12          6          0          0         12        707   wdg.o

    ----------------------------------------------------------------------
     17872       <USER>       <GROUP>        228      10188     984360   Object Totals
         0          0         48          0          0          0   (incl. Generated)
        30          0          8          3          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2358         96          0          0          0        764   printfa.o
        28          0          0          0          0         76   strcmp.o
        14          0          0          0          0         68   strlen.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      4104        <USER>          <GROUP>          0          0       2584   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2802        112          0          0          0       1528   mc_w.l
      1296          0          0          0          0       1056   mf_w.l

    ----------------------------------------------------------------------
      4104        <USER>          <GROUP>          0          0       2584   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     21976       1868       4076        228      10188     967496   Grand Totals
     21976       1868       4076        228      10188     967496   ELF Image Totals
     21976       1868       4076        228          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                26052 (  25.44kB)
    Total RW  Size (RW Data + ZI Data)             10416 (  10.17kB)
    Total ROM Size (Code + RO Data + RW Data)      26280 (  25.66kB)

==============================================================================

