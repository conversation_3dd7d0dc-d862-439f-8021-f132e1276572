#include "./drivers/core/driver_core.h"
#include "./system/sys/sys.h"

TIM_HandleTypeDef tim1_pwm_handle;
TIM_HandleTypeDef tim2_pwm_handle;
TIM_HandleTypeDef tim3_pwm_handle;
TIM_HandleTypeDef tim4_pwm_handle;
TIM_HandleTypeDef tim5_pwm_handle;
TIM_HandleTypeDef tim6_pwm_handle;
TIM_HandleTypeDef tim7_pwm_handle;
TIM_HandleTypeDef tim8_pwm_handle;
TIM_HandleTypeDef tim9_pwm_handle;
TIM_HandleTypeDef tim10_pwm_handle;

/**
 * @brief        TIM PWM引脚初始化
 * @param       tim_id       TIM ID号
 * @param       channel      PWM通道
 * @param       pin          PWM引脚
 * @param       arr          自动重装载值
 * @param       psc          预分频值
 * @param       alternate    复用功能
 * @note        无
 * @retval      无
 */
void tim_pwm_pin_init(uint8_t tim_id, uint8_t channel, uint8_t pin, uint32_t arr, uint32_t psc)
{
    TIM_HandleTypeDef *tim_pwm_handle = NULL;
    uint8_t alternate = 0;
    if (tim_id == 1)
    {
        tim_pwm_handle = &tim1_pwm_handle;
        tim1_pwm_handle.Instance = TIM1;
        __HAL_RCC_TIM1_CLK_ENABLE();
        alternate = AF_TIM1;
    }
    else if (tim_id == 2)
    {
        tim_pwm_handle = &tim2_pwm_handle;
        tim2_pwm_handle.Instance = TIM2;
        __HAL_RCC_TIM2_CLK_ENABLE();
        alternate = AF_TIM2;
    }
    else if (tim_id == 3)
    {
        tim_pwm_handle = &tim3_pwm_handle;
        tim3_pwm_handle.Instance = TIM3;
        __HAL_RCC_TIM3_CLK_ENABLE();
        alternate = AF_TIM3;
    }
    else if (tim_id == 4)
    {
        tim_pwm_handle = &tim4_pwm_handle;
        tim4_pwm_handle.Instance = TIM4;
        __HAL_RCC_TIM4_CLK_ENABLE();
        alternate = AF_TIM4;
    }
    else if (tim_id == 5)
    {
        tim_pwm_handle = &tim5_pwm_handle;
        tim5_pwm_handle.Instance = TIM5;
        __HAL_RCC_TIM5_CLK_ENABLE();
        alternate = AF_TIM5;
    }
    else if (tim_id == 6)
    {
        tim_pwm_handle = &tim6_pwm_handle;
        tim6_pwm_handle.Instance = TIM6;
        __HAL_RCC_TIM6_CLK_ENABLE();
        alternate = AF_TIM6;
    }
    else if (tim_id == 7)
    {
        tim_pwm_handle = &tim7_pwm_handle;
        tim7_pwm_handle.Instance = TIM7;
        __HAL_RCC_TIM7_CLK_ENABLE();
        alternate = AF_TIM7;
    }
    else if (tim_id == 8)
    {
        tim_pwm_handle = &tim8_pwm_handle;
        tim8_pwm_handle.Instance = TIM8;
        __HAL_RCC_TIM8_CLK_ENABLE();
        alternate = AF_TIM8;
    }
    else if (tim_id == 9)
    {
        tim_pwm_handle = &tim9_pwm_handle;
        tim9_pwm_handle.Instance = TIM9;
        __HAL_RCC_TIM9_CLK_ENABLE();
        alternate = AF_TIM9;
    }
    else if (tim_id == 10)
    {
        tim_pwm_handle = &tim10_pwm_handle;
        tim10_pwm_handle.Instance = TIM10;
        __HAL_RCC_TIM10_CLK_ENABLE();
        alternate = AF_TIM10;
    }

    TIM_OC_InitTypeDef timx_oc_pwm_chy = {0};              /* 定时器输出句柄 */
    tim_pwm_handle->Init.Prescaler = psc;                  /* 预分频系数 */
    tim_pwm_handle->Init.CounterMode = TIM_COUNTERMODE_UP; /* 递增计数模式 */
    tim_pwm_handle->Init.Period = arr;                     /* 自动重装载值 */
    HAL_TIM_PWM_Init(tim_pwm_handle);                      /* 初始化PWM */

    timx_oc_pwm_chy.OCMode = TIM_OCMODE_PWM1;        /* 模式选择PWM1 */
    timx_oc_pwm_chy.Pulse = arr / 2;                 /* 设置比较值,此值用来确定占空比 */
    timx_oc_pwm_chy.OCPolarity = TIM_OCPOLARITY_LOW; /* 输出比较极性为低 */

    uint32_t pwm_channel;
    if (channel == 1)
        pwm_channel = TIM_CHANNEL_1;
    else if (channel == 2)
        pwm_channel = TIM_CHANNEL_2;
    else if (channel == 3)
        pwm_channel = TIM_CHANNEL_3;
    else if (channel == 4)
        pwm_channel = TIM_CHANNEL_4;

    HAL_TIM_PWM_ConfigChannel(tim_pwm_handle, &timx_oc_pwm_chy, pwm_channel); /* 配置TIMx通道y */
    HAL_TIM_PWM_Start(tim_pwm_handle, pwm_channel);                           /* 开启对应PWM通道 */

    gpio_pin_init_alternate(pin, AF_PP, PULLUP, HIGH, alternate);
}

/**
 * @brief       设置PWM比较值
 * @param       tim_id       TIM ID号
 * @param       channel      PWM通道
 * @param       ccr          比较值
 * @note        无
 * @retval      无
 */
void tim_pwm_set_ccr(uint8_t tim_id, uint8_t channel, uint32_t ccr)
{
    TIM_HandleTypeDef *tim_pwm_handle = NULL;
    if (tim_id == 1)
    {
        tim_pwm_handle = &tim1_pwm_handle;
    }
    else if (tim_id == 2)
    {
        tim_pwm_handle = &tim2_pwm_handle;
    }
    else if (tim_id == 3)
    {
        tim_pwm_handle = &tim3_pwm_handle;
    }
    else if (tim_id == 4)
    {
        tim_pwm_handle = &tim4_pwm_handle;
    }
    else if (tim_id == 5)
    {
        tim_pwm_handle = &tim5_pwm_handle;
    }
    else if (tim_id == 6)
    {
        tim_pwm_handle = &tim6_pwm_handle;
    }
    else if (tim_id == 7)
    {
        tim_pwm_handle = &tim7_pwm_handle;
    }
    else if (tim_id == 8)
    {
        tim_pwm_handle = &tim8_pwm_handle;
    }
    else if (tim_id == 9)
    {
        tim_pwm_handle = &tim9_pwm_handle;
    }
    else if (tim_id == 10)
    {
        tim_pwm_handle = &tim10_pwm_handle;
    }

    uint32_t pwm_channel;
    if (channel == 1)
        pwm_channel = TIM_CHANNEL_1;
    else if (channel == 2)
        pwm_channel = TIM_CHANNEL_2;
    else if (channel == 3)
        pwm_channel = TIM_CHANNEL_3;
    else if (channel == 4)
        pwm_channel = TIM_CHANNEL_4;

    __HAL_TIM_SET_COMPARE(tim_pwm_handle, pwm_channel, ccr);
}
