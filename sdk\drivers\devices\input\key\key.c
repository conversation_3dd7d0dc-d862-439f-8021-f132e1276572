#include "./drivers/devices/input/key/key.h"
#include "./system/debug/debug.h"
#include "./drivers/devices/input/key/gpio_key/gpio_key.h"
#include "./drivers/devices/input/key/adc_key/adc_key.h"
#include "./config/board_config.h"

void key_init(void)
{
#if TCFG_IO_KEY_EN == TCFG_ENABLE
    gpio_key_init();
#endif
#if TCFG_ADC_KEY_EN == TCFG_ENABLE
    adc_key_init();
#endif
}

void process_key_event(struct key_event *e)
{
    DEBUG_INFO("key event: %d, value: %d\r\n", e->event, e->value);
    switch (e->type)
    {
    case KEY_DRIVER_TYPE_IO:
#if TCFG_IO_KEY_EN == TCFG_ENABLE
        process_gpio_key_event(e);
#endif
        break;
    case KEY_DRIVER_TYPE_ADC:
#if TCFG_ADC_KEY_EN == TCFG_ENABLE
        process_adc_key_event(e);
#endif
        break;
    default:
        break;
    }
}
