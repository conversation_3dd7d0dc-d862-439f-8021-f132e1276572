#include "mydefine.h" // Device header
#include "Encoder.h"

///* 减速电机减速比 */
//#define Reduction_ratio 28

///* 编码器物理分辨率 */ // 即编码器线数或者叫精度
//#define ENCODER_RESOLUTION 500

///* 经过倍频之后的总分辨率 */
//#define Frequency_Divide (ENCODER_RESOLUTION * 4) /* 4倍频后的总分辨率 */

//#define WheelR 2.4

////测速
// void Right_Encoder_Init(void)
//{
//	//开启时钟
// RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM5, ENABLE);       // TIM5挂在APB1总线
// RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);     // GPIOA时钟

////配置GPIO（TIM5的CH1和CH2对应PA0和PA1）
// GPIO_InitTypeDef GPIO_InitStructure;
// GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;             // 上拉输入（根据编码器信号实际情况选择）
// GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1;    // PA0(TIM5_CH1), PA1(TIM5_CH2)
// GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
// GPIO_Init(GPIOA, &GPIO_InitStructure);

////配置时基单元
// TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
// TIM_TimeBaseInitStructure.TIM_ClockDivision = TIM_CKD_DIV1;
// TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up;
// TIM_TimeBaseInitStructure.TIM_Period = 65536 - 1;         // ARR（32位定时器可设置更大值）
// TIM_TimeBaseInitStructure.TIM_Prescaler = 1 - 1;          // PSC（分频器，不分频）
// TIM_TimeBaseInitStructure.TIM_RepetitionCounter = 0;      // 高级定时器参数，TIM5忽略此配置
// TIM_TimeBaseInit(TIM5, &TIM_TimeBaseInitStructure);

////配置输入捕获通道
// TIM_ICInitTypeDef TIM_ICInitStructure;
// TIM_ICStructInit(&TIM_ICInitStructure);                   // 加载默认参数
// TIM_ICInitStructure.TIM_Channel = TIM_Channel_1;          // 通道1（PA0）
// TIM_ICInitStructure.TIM_ICFilter = 0xF;                   // 输入滤波（根据噪声情况调整）
// TIM_ICInit(TIM5, &TIM_ICInitStructure);

// TIM_ICInitStructure.TIM_Channel = TIM_Channel_2;          // 通道2（PA1）
// TIM_ICInitStructure.TIM_ICFilter = 0xF;
// TIM_ICInit(TIM5, &TIM_ICInitStructure);

////配置编码器接口模式
// TIM_EncoderInterfaceConfig(
//     TIM5,
//     TIM_EncoderMode_TI12,      // 使用TI1和TI2边沿计数
//     TIM_ICPolarity_Rising,     // 不反相
//     TIM_ICPolarity_Rising
//);

////使能定时器
// TIM_Cmd(TIM5, ENABLE);
// }

// void Left_Encoder_Init(void)
//{
//	/*开启时钟*/
//	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);			//开启TIM3的时钟
//	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);			//开启GPIOA的时钟
//
//	/*GPIO初始化*/
//	GPIO_InitTypeDef GPIO_InitStructure;
//	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
//	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_6 | GPIO_Pin_7;
//	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
//	GPIO_Init(GPIOA, &GPIO_InitStructure);							//将PA6和PA7引脚初始化为上拉输入
//
//	/*时基单元初始化*/
//	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;				//定义结构体变量
//	TIM_TimeBaseInitStructure.TIM_ClockDivision = TIM_CKD_DIV1;     //时钟分频，选择不分频，此参数用于配置滤波器时钟，不影响时基单元功能
//	TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up; //计数器模式，选择向上计数
//	TIM_TimeBaseInitStructure.TIM_Period = 65536 - 1;               //计数周期，即ARR的值
//	TIM_TimeBaseInitStructure.TIM_Prescaler = 1 - 1;                //预分频器，即PSC的值
//	TIM_TimeBaseInitStructure.TIM_RepetitionCounter = 0;            //重复计数器，高级定时器才会用到
//	TIM_TimeBaseInit(TIM3, &TIM_TimeBaseInitStructure);             //将结构体变量交给TIM_TimeBaseInit，配置TIM3的时基单元
//
//	/*输入捕获初始化*/
//	TIM_ICInitTypeDef TIM_ICInitStructure;							//定义结构体变量
//	TIM_ICStructInit(&TIM_ICInitStructure);							//结构体初始化，若结构体没有完整赋值
//																	//则最好执行此函数，给结构体所有成员都赋一个默认值
//																	//避免结构体初值不确定的问题
//	TIM_ICInitStructure.TIM_Channel = TIM_Channel_1;				//选择配置定时器通道1
//	TIM_ICInitStructure.TIM_ICFilter = 0xF;							//输入滤波器参数，可以过滤信号抖动
//	TIM_ICInit(TIM3, &TIM_ICInitStructure);							//将结构体变量交给TIM_ICInit，配置TIM3的输入捕获通道
//	TIM_ICInitStructure.TIM_Channel = TIM_Channel_2;				//选择配置定时器通道2
//	TIM_ICInitStructure.TIM_ICFilter = 0xF;							//输入滤波器参数，可以过滤信号抖动
//	TIM_ICInit(TIM3, &TIM_ICInitStructure);							//将结构体变量交给TIM_ICInit，配置TIM3的输入捕获通道
//
//	/*编码器接口配置*/
//	TIM_EncoderInterfaceConfig(TIM3, TIM_EncoderMode_TI12, TIM_ICPolarity_Rising, TIM_ICPolarity_Rising);
//																	//配置编码器模式以及两个输入通道是否反相
//																	//注意此时参数的Rising和Falling已经不代表上升沿和下降沿了，而是代表是否反相
//																	//此函数必须在输入捕获初始化之后进行，否则输入捕获的配置会覆盖此函数的部分配置
//
//	/*TIM使能*/
//	TIM_Cmd(TIM3, ENABLE);			//使能TIM3，定时器开始运行
// }

// int16_t Right_Encoder_Get(void)  	//
//{
//	int16_t Temp_r;
//	Temp_r = TIM_GetCounter(TIM5); //这里捕获到的是转动时的脉冲数，如果要将其转换为对应轮子的转速，要换算，减速比为1:20，精度为500，所以换算下来为
//																//轮子半径为24mm，一圈要走150.72mm，而这个电机转一圈应该是40000个脉冲（减速比*精度*分频），所以，这里获取到编码器的脉冲，
//																//就可以知道小车走过的距离，如果在把这个函数放到定时器中断里，就可以计算出小车的速度。
//	TIM_SetCounter(TIM5, 0);
//	return Temp_r;
// }

// int16_t Left_Encoder_Get(void)
//{
//	int16_t Temp_l;
//	Temp_l = TIM_GetCounter(TIM3); //这里捕获到的是转动时的脉冲数，如果要将其转换为对应轮子的转速，要换算，减速比为1:20，精度为500，所以换算下来为
//																//轮子半径为24mm，一圈要走150.72mm，而这个电机转一圈应该是40000个脉冲（减速比*精度*分频），所以，这里获取到编码器的脉冲，
//																//就可以知道小车走过的距离，如果在把这个函数放到定时器中断里，就可以计算出小车的速度。
//	TIM_SetCounter(TIM3, 0);
//	return Temp_l;
// }

long g_lMotor_Lef_PulseSigma = 0;					  // 位置外环使用的脉冲累积
long g_lMotor_Rig_PulseSigma = 0;					  // 位置外环使用的脉冲累积
short g_nMotor_Lef_Pulse = 0, g_nMotor_Rig_Pulse = 0; // 全局变量， 保存电机脉冲数值

/*******************实际运行时读取编码器数值************************/ // 当两个轮子转向不一致的时候，要同时调这里编码器捕获脉冲的正负和control.h里的PWM通道宏定义
void GetMotorPulse(void)											  // 读取电机脉冲
{

	g_nMotor_Rig_Pulse = (short)(__HAL_TIM_GET_COUNTER(&htim1)); // 获取右编码器计数器值   为什么现在可以两个都是正？
	__HAL_TIM_SET_COUNTER(&htim1, 0);							 // 右编码器TIM5计数器清零
	// TIM_SetCounter(TIM5, 0);

	g_nMotor_Lef_Pulse = (short)(__HAL_TIM_GET_COUNTER(&htim3)); // 获取计数器值
	__HAL_TIM_SET_COUNTER(&htim3, 0);							 // 左编码器TIM3计数器清零
	// TIM_SetCounter(TIM3, 0);

	g_lMotor_Rig_PulseSigma += g_nMotor_Rig_Pulse; // 右位置外环使用的脉冲累积      //完成一次位置控制之后才清除。
	g_lMotor_Lef_PulseSigma += g_nMotor_Lef_Pulse; // 左位置外环使用的脉冲累积   //记录了之后也要及时清零呀！！！
}
