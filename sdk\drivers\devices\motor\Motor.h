#ifndef __MOTOR_H__
#define __MOTOR_H__

#include "main.h"
#include "device.h"
#include "Control.h"

// void SET_R_FWD_COMPAER(ChannelPulse);
// void SET_R_REV_COMPAER(ChannelPulse);
// void SET_L_FWD_COMPAER(ChannelPulse);
// void SET_L_REV_COMPAER(ChannelPulse);
// void MOTORDISABLE();

// 小车整体控制
void car_move(void);

void Speed_Control(uint16_t Right, uint16_t Left); // 电机设置速度
void run(uint16_t Right, uint16_t Left);		   // 前进
void backrun(uint16_t Right, uint16_t Left);	   // 后退
void leftturn(uint16_t Right, uint16_t Left);	   // 左转
void rightturn(uint16_t Right, uint16_t Left);	   // 右转
void stop(void);

int Motor_All_Init(struct device *dev); // 左右电机、编码器、PID初始化

// 右
void Right_moto_go(struct device *dev); // 电机正转
void Right_moto_back(struct device *dev); // 电机反转
void Right_moto_Stop(struct device *dev); // 电机停
										  // 左
void Left_moto_go(struct device *dev);
void Left_moto_back(struct device *dev);
void Left_moto_Stop(struct device *dev);

void Left_PWM_SetCompare(uint16_t Compare);
void Right_PWM_SetCompare(uint16_t Compare);
#endif
